// Ownership of this type is configured via Fluent API in the DbContext

namespace GreenFlow.Storage.Entities;

public class CoordinationRule
{
    public string? RequiredAssignee { get; set; }

    public List<string> OptionalAssignees { get; set; } = [];

    public int RequiredOptionalCount { get; set; } = 1;

    public string? DynamicGroupQuery { get; set; } 

    public bool WaitForAll { get; set; } = false;

    public bool AllowWildcardMatching { get; set; } = true; 
}