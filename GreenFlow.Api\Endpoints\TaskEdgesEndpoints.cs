using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using GreenFlow.Shared;

namespace GreenFlow.Api.Endpoints;

public static class TaskEdgesEndpoints
{
    public static void MapTaskEdgesEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/task-edges").WithTags("TaskEdges");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            var edges = await db.Edges
                .Include(e => e.FromTask)
                .Include(e => e.ToTask)
                .ToListAsync();
            var dtos = mapper.Map<List<TaskEdgeDto>>(edges);
            return Results.Ok(dtos);
        })
        .WithName("GetTaskEdges");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var edge = await db.Edges
                .Include(e => e.FromTask)
                .Include(e => e.ToTask)
                .FirstOrDefaultAsync(e => e.Id == id);
            return edge is not null
                ? Results.Ok(mapper.Map<TaskEdgeDto>(edge))
                : Results.NotFound();
        })
        .WithName("GetTaskEdgeById");

        group.MapPost("/", async (CreateTaskEdgeDto createDto, FlowDbContext db, IMapper mapper) =>
        {
            var edge = mapper.Map<TaskEdge>(createDto);
            edge.Id = Guid.NewGuid();
            db.Edges.Add(edge);
            await db.SaveChangesAsync();
            var result = mapper.Map<TaskEdgeDto>(edge);
            return Results.Created($"/api/task-edges/{edge.Id}", result);
        })
        .WithName("CreateTaskEdge");

        group.MapPut("/{id:guid}", async (Guid id, UpdateTaskEdgeDto updateDto, FlowDbContext db, IMapper mapper) =>
        {
            var edge = await db.Edges.FindAsync(id);
            if (edge is null)
                return Results.NotFound();

            mapper.Map(updateDto, edge);
            await db.SaveChangesAsync();
            return Results.Ok(mapper.Map<TaskEdgeDto>(edge));
        })
        .WithName("UpdateTaskEdge");

        group.MapDelete("/{id:guid}", async (Guid id, FlowDbContext db) =>
        {
            var edge = await db.Edges.FindAsync(id);
            if (edge is null)
                return Results.NotFound();

            db.Edges.Remove(edge);
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("DeleteTaskEdge");

        // Get edges for a specific flow
        group.MapGet("/flow/{flowId:guid}", async (Guid flowId, FlowDbContext db, IMapper mapper) =>
        {
            var edges = await db.Edges
                .Include(e => e.FromTask)
                .Include(e => e.ToTask)
                .Where(e => e.FromTask.FlowId == flowId)
                .ToListAsync();
            var dtos = mapper.Map<List<TaskEdgeDto>>(edges);
            return Results.Ok(dtos);
        })
        .WithName("GetTaskEdgesByFlow");

        // Lookup for EdgeType enum
        group.MapGet("/edge-types", () =>
            Results.Ok(Enum.GetValues(typeof(EdgeType))))
            .WithName("GetEdgeTypes");
    }
}
