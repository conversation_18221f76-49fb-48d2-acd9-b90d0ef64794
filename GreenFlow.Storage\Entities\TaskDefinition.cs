﻿namespace GreenFlow.Storage.Entities;

public class TaskDefinition
{
    public Guid Id { get; set; }
    public Guid FlowId { get; set; }
    public FlowDefinition Flow { get; set; } = null!;
    public string Name { get; set; } = null!;
    public ActionType ActionType { get; set; }
    public string? ActionPayload { get; set; }
    public List<TaskDefinition>? SubTasks { get; set; }
    public Guid? ParentTaskId { get; set; }
    public CoordinationRule? CoordinationRule { get; set; }
    public double? RuntimeCostMultiplier { get; set; }
    public int PositionX { get; set; }
    public int PositionY { get; set; }

    /// <summary>
    /// Field-level validation rules (e.g. required, min/max, regex, external checks).
    /// </summary>
    public List<FieldRule> FieldRules { get; set; } = new();

    /// <summary>
    /// Cross-field validation rules (e.g. startDate &lt;= endDate).
    /// </summary>
    public List<CrossFieldRule> CrossFieldRules { get; set; } = new();
}


