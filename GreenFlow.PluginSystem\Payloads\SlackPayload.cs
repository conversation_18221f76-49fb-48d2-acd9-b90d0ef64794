using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for Slack notification tasks.
    /// </summary>
    public class SlackPayload : ITaskPayload
    {
        /// <summary>
        /// The Slack channel to send the message to.
        /// </summary>
        public string Channel { get; set; } = null!;

        /// <summary>
        /// The message text to send.
        /// </summary>
        public string Message { get; set; } = null!;

        /// <summary>
        /// Optional username to display as sender.
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// Optional emoji icon for the message.
        /// </summary>
        public string? IconEmoji { get; set; }

        /// <summary>
        /// Optional attachments for rich formatting.
        /// </summary>
        public List<SlackAttachment>? Attachments { get; set; }

        /// <inheritdoc />
        public ActionType ActionType => ActionType.Slack;
    }

    /// <summary>
    /// Represents a Slack message attachment.
    /// </summary>
    public class SlackAttachment
    {
        public string? Title { get; set; }
        public string? Text { get; set; }
        public string? Color { get; set; }
        public List<SlackField>? Fields { get; set; }
    }

    /// <summary>
    /// Represents a field in a Slack attachment.
    /// </summary>
    public class SlackField
    {
        public string Title { get; set; } = null!;
        public string Value { get; set; } = null!;
        public bool Short { get; set; } = false;
    }
}
