using GreenFlow.PluginSystem.Interfaces;
// Removed attribute-based mapping; using instance property instead
using GreenFlow.Storage.Entities;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for Loop tasks.
    /// </summary>
    public class LoopPayload : ITaskPayload
    {
        /// <summary>
        /// The path in context to the collection to iterate.
        /// </summary>
        public string CollectionPath { get; set; } = null!;

        /// <summary>
        /// The variable name for each iteration item.
        /// </summary>
        public string IteratorName { get; set; } = null!;
        /// <inheritdoc />
        public ActionType ActionType => ActionType.Loop;
    }
}