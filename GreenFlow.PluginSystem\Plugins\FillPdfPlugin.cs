using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to fill a PDF based on FillPdfPayload.
    /// </summary>
    public class FillPdfPlugin : ITaskActionHandler<FillPdfPayload>
    {
        public Task HandleAsync(FillPdfPayload payload, IDictionary<string, object> context)
        {
            // For demonstration, store filled PDF info in context
            context["FilledPdf"] = new { payload.PdfTemplate, payload.Fields, payload.OutputPath };
            return Task.CompletedTask;
        }
    }
}