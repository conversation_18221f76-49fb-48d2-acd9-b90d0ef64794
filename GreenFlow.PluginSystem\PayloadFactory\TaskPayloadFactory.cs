using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.PluginSystem.PayloadFactory
{
    /// <summary>
    /// Factory to create task payloads based on ActionType.
    /// </summary>
    public static class TaskPayloadFactory
    {
        private static readonly Dictionary<ActionType, Type> _payloadTypes;

        static TaskPayloadFactory()
        {
            // Discover all payload types implementing ITaskPayload with default ctor
            _payloadTypes = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => !a.IsDynamic && !string.IsNullOrEmpty(a.Location))
                .SelectMany(a => a.GetTypes())
                .Where(t => typeof(Interfaces.ITaskPayload).IsAssignableFrom(t)
                            && t.IsClass && !t.IsAbstract
                            && t.GetConstructor(Type.EmptyTypes) != null)
                .Select(t =>
                {
                    // Instantiate to read the ActionType property
                    var payload = (Interfaces.ITaskPayload)Activator.CreateInstance(t)!;
                    return (ActionType: payload.ActionType, Type: t);
                })
                .ToDictionary(x => x.ActionType, x => x.Type!);
        }

        /// <summary>
        /// Deserializes the payload JSON into the appropriate payload type.
        /// </summary>
        /// <param name="payloadJson">The JSON payload.</param>
        /// <param name="actionType">The ActionType for the task.</param>
        /// <returns>An instance of ITaskPayload.</returns>
        public static ITaskPayload Create(string payloadJson, ActionType actionType)
        {
            if (!_payloadTypes.TryGetValue(actionType, out var type))
                throw new InvalidOperationException($"No payload type registered for action {actionType}");

            return (ITaskPayload)JsonSerializer.Deserialize(payloadJson, type)
                   ?? throw new InvalidOperationException($"Failed to deserialize payload for {actionType}");
        }
    }
}