using GreenFlow.Shared;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GreenFlow.Engine.Coordination;

/// <summary>
/// Evaluates coordination rules for task execution.
/// </summary>
public class CoordinationRuleEvaluator
{
    private readonly FlowDbContext _dbContext;
    private readonly ILogger<CoordinationRuleEvaluator> _logger;

    public CoordinationRuleEvaluator(FlowDbContext dbContext, ILogger<CoordinationRuleEvaluator> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Evaluates if a task can be executed based on its coordination rules.
    /// </summary>
    public async Task<bool> CanExecuteTaskAsync(Guid executionId, Guid taskId, UserIdentity? userIdentity, CancellationToken cancellationToken = default)
    {
        var task = await _dbContext.Tasks
            .Include(t => t.CoordinationRule)
            .FirstOrDefaultAsync(t => t.Id == taskId, cancellationToken);

        if (task?.CoordinationRule == null)
        {
            // No coordination rules, task can be executed
            return true;
        }

        var rule = task.CoordinationRule;
        _logger.LogDebug("Evaluating coordination rule for task {TaskId}", taskId);

        // Check required assignee
        if (!string.IsNullOrEmpty(rule.RequiredAssignee))
        {
            if (userIdentity == null || !IsUserMatch(userIdentity, rule.RequiredAssignee))
            {
                _logger.LogDebug("Task {TaskId} requires assignee {RequiredAssignee}, but user is {UserId}",
                    taskId, rule.RequiredAssignee, userIdentity?.UserId);
                return false;
            }
        }

        // Check optional assignees
        if (rule.OptionalAssignees.Any())
        {
            var matchingOptionalCount = await CountMatchingOptionalAssigneesAsync(executionId, taskId, rule, cancellationToken);

            if (matchingOptionalCount < rule.RequiredOptionalCount)
            {
                _logger.LogDebug("Task {TaskId} requires {RequiredCount} optional assignees, but only {ActualCount} have completed",
                    taskId, rule.RequiredOptionalCount, matchingOptionalCount);
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// Evaluates if all coordination requirements are met for a task.
    /// </summary>
    public async Task<bool> AreCoordinationRequirementsMetAsync(Guid executionId, Guid taskId, CancellationToken cancellationToken = default)
    {
        var task = await _dbContext.Tasks
            .Include(t => t.CoordinationRule)
            .FirstOrDefaultAsync(t => t.Id == taskId, cancellationToken);

        if (task?.CoordinationRule == null)
        {
            return true; // No coordination requirements
        }

        var rule = task.CoordinationRule;

        // If WaitForAll is true, check that all required participants have completed their parts
        if (rule.WaitForAll)
        {
            return await CheckAllParticipantsCompletedAsync(executionId, taskId, rule, cancellationToken);
        }

        // Otherwise, check if minimum requirements are met
        return await CheckMinimumRequirementsMetAsync(executionId, taskId, rule, cancellationToken);
    }

    private static bool IsUserMatch(UserIdentity userIdentity, string assignee)
    {
        // Check if user ID, username, or email matches the assignee
        return userIdentity.UserId.Equals(assignee, StringComparison.OrdinalIgnoreCase) ||
               userIdentity.UserName.Equals(assignee, StringComparison.OrdinalIgnoreCase) ||
               userIdentity.Email.Equals(assignee, StringComparison.OrdinalIgnoreCase) ||
               userIdentity.Roles.Contains(assignee, StringComparer.OrdinalIgnoreCase);
    }

    private async Task<int> CountMatchingOptionalAssigneesAsync(Guid executionId, Guid taskId, CoordinationRule rule, CancellationToken cancellationToken)
    {
        // This would typically check completed user forms or task executions
        // For now, return 0 as a placeholder
        await Task.CompletedTask;
        return 0;
    }

    private async Task<bool> CheckAllParticipantsCompletedAsync(Guid executionId, Guid taskId, CoordinationRule rule, CancellationToken cancellationToken)
    {
        // Check if all required and optional assignees have completed their tasks
        await Task.CompletedTask;
        return true; // Placeholder implementation
    }

    private async Task<bool> CheckMinimumRequirementsMetAsync(Guid executionId, Guid taskId, CoordinationRule rule, CancellationToken cancellationToken)
    {
        // Check if minimum coordination requirements are met
        var optionalCount = await CountMatchingOptionalAssigneesAsync(executionId, taskId, rule, cancellationToken);
        return optionalCount >= rule.RequiredOptionalCount;
    }
}