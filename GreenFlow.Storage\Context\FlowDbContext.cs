using GreenFlow.Storage.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Text.Json;

namespace GreenFlow.Storage.Context;

public class FlowDbContext(DbContextOptions<FlowDbContext> options) : DbContext(options)
{
    public DbSet<FlowDefinition> Flows => Set<FlowDefinition>();
    public DbSet<TaskDefinition> Tasks => Set<TaskDefinition>();
    public DbSet<TaskEdge> Edges => Set<TaskEdge>();
    public DbSet<FlowExecution> Executions => Set<FlowExecution>();
    public DbSet<TaskExecution> TaskExecutions => Set<TaskExecution>();
    public DbSet<Tenant> Tenants => Set<Tenant>();
    public DbSet<SlaConfig> Slas => Set<SlaConfig>();
    public DbSet<PendingUserForm> PendingForms => Set<PendingUserForm>();
    public DbSet<TokenLedgerEntry> TokenLedger => Set<TokenLedgerEntry>();
    public DbSet<ExecutionPauseState> PausedExecutions => Set<ExecutionPauseState>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Apply all IEntityTypeConfiguration<T> implementations if any exist
        var assembly = typeof(FlowDbContext).Assembly;
        var hasConfigurations = assembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract)
            .SelectMany(t => t.GetInterfaces(), (t, i) => new { Type = t, Interface = i })
            .Any(x => x.Interface.IsGenericType
                      && x.Interface.GetGenericTypeDefinition() == typeof(IEntityTypeConfiguration<>));
        
        if (hasConfigurations)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(assembly);
        }

        // Map dictionary properties to JSONB columns in Postgres
        modelBuilder.Entity<FlowExecution>(b =>
        {
            b.Property(e => e.Input).HasColumnType("jsonb");
            b.Property(e => e.Context).HasColumnType("jsonb");
            b.Property(e => e.Status).HasConversion<string>();
        });
        modelBuilder.Entity<TaskExecution>(b =>
        {
            b.Property(te => te.InputSnapshot).HasColumnType("jsonb");
            b.Property(te => te.OutputSnapshot).HasColumnType("jsonb");
            b.Property(te => te.Status).HasConversion<string>();
        });
        modelBuilder.Entity<PendingUserForm>(b =>
        {
            b.Property(pf => pf.Input).HasColumnType("jsonb");
        });
        // Configure TaskEdge: store EdgeType as string and map failure threshold
        modelBuilder.Entity<TaskEdge>(b =>
        {
            b.Property(e => e.Type).HasConversion<string>();
            b.Property(e => e.FailureThreshold);
        });
        // Store enums as strings and configure owned CoordinationRule
        _ = modelBuilder.Entity<TaskDefinition>(b =>
        {
            // ActionType enum as string
            b.Property(t => t.ActionType).HasConversion<string>();

            // Owned CoordinationRule mapping
            _ = b.OwnsOne(t => t.CoordinationRule, cr =>
            {
                cr.Property(c => c.RequiredAssignee);
                cr.Property(c => c.DynamicGroupQuery);
                cr.Property(c => c.RequiredOptionalCount);
                cr.Property(c => c.WaitForAll);
                cr.Property(c => c.AllowWildcardMatching);

                // Configure OptionalAssignees as JSONB with value converter and comparer
                var optionalAssigneesConverter = new ValueConverter<List<string>, string>(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<List<string>>(v, JsonSerializerOptions.Default) ?? new List<string>());
                var optionalAssigneesComparer = new ValueComparer<List<string>>(
                    (a, b) => a!.SequenceEqual(b ?? new List<string>()),
                    a => a.Aggregate(0, (h, s) => HashCode.Combine(h, s != null ? s.GetHashCode() : 0)),
                    a => a.ToList());
                var optionalProp = cr.Property(c => c.OptionalAssignees)
                    .HasConversion(optionalAssigneesConverter)
                    .HasColumnType("jsonb");
                optionalProp.Metadata.SetValueComparer(optionalAssigneesComparer);
            });
        });
        // Field-level rules as regular entities with FK
        modelBuilder.Entity<FieldRule>(b =>
        {
            b.ToTable("TaskFieldRules");
            b.HasKey(r => r.Id);
            b.Property(r => r.FieldName).IsRequired();
            b.Property(r => r.Required);
            b.Property(r => r.Min);
            b.Property(r => r.Max);
            b.Property(r => r.RegexPattern);
            b.Property(r => r.ExternalValidator);
            b.HasOne(r => r.Task)
             .WithMany(t => t.FieldRules)
             .HasForeignKey(r => r.TaskId);
        });
        // Cross-field rules as regular entities with FK
        modelBuilder.Entity<CrossFieldRule>(b =>
        {
            b.ToTable("TaskCrossFieldRules");
            b.HasKey(r => r.Id);
            b.Property(r => r.FieldNameA).IsRequired();
            b.Property(r => r.FieldNameB).IsRequired();
            b.Property(r => r.Comparison).HasConversion<string>();
            b.Property(r => r.ErrorMessage);
            b.HasOne(r => r.Task)
             .WithMany(t => t.CrossFieldRules)
             .HasForeignKey(r => r.TaskId);
        });
        modelBuilder.Entity<ExecutionPauseState>(b =>
        {
            b.Property(eps => eps.Reason).HasConversion<string>();
        });

        base.OnModelCreating(modelBuilder);
    }
}