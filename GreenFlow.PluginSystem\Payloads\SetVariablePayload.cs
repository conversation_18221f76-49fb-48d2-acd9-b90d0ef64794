using System.Collections.Generic;
using GreenFlow.PluginSystem.Interfaces;
// Removed attribute-based mapping; using instance property instead
using GreenFlow.Storage.Entities;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for SetVariable tasks.
    /// </summary>
    public class SetVariablePayload : ITaskPayload
    {
        /// <summary>
        /// Variables to set in context.
        /// </summary>
        public Dictionary<string, object> Variables { get; set; } = new();
        /// <inheritdoc />
        public ActionType ActionType => ActionType.SetVariable;
    }
}