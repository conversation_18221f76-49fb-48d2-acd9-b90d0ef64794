using GreenFlow.Shared;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GreenFlow.Engine.Execution;

/// <summary>
/// Main orchestration engine for task flow execution.
/// </summary>
public class TaskFlowEngine : ITaskFlowEngine
{
    private readonly FlowDbContext _dbContext;
    private readonly FlowExecutor _flowExecutor;
    private readonly ILogger<TaskFlowEngine> _logger;

    public TaskFlowEngine(
        FlowDbContext dbContext,
        FlowExecutor flowExecutor,
        ILogger<TaskFlowEngine> logger)
    {
        _dbContext = dbContext;
        _flowExecutor = flowExecutor;
        _logger = logger;
    }

    /// <summary>
    /// Starts a flow execution by its identifier.
    /// </summary>
    public async Task StartAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting flow execution {ExecutionId}", executionId);

        var execution = await _dbContext.Executions
            .Include(e => e.Flow)
            .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

        if (execution == null)
        {
            _logger.LogError("Flow execution {ExecutionId} not found", executionId);
            throw new InvalidOperationException($"Flow execution {executionId} not found");
        }

        if (execution.Status != ExecutionStatus.Running)
        {
            _logger.LogWarning("Flow execution {ExecutionId} is not in Running status: {Status}",
                executionId, execution.Status);
            return;
        }

        var success = await _flowExecutor.StartFlowAsync(executionId, cancellationToken);
        if (!success)
        {
            _logger.LogError("Failed to start flow execution {ExecutionId}", executionId);
            throw new InvalidOperationException($"Failed to start flow execution {executionId}");
        }

        _logger.LogInformation("Flow execution {ExecutionId} started successfully", executionId);
    }

    /// <summary>
    /// Resumes a paused execution.
    /// </summary>
    public async Task ResumeAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Resuming flow execution {ExecutionId}", executionId);

        var execution = await _dbContext.Executions
            .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

        if (execution == null)
        {
            _logger.LogError("Flow execution {ExecutionId} not found", executionId);
            throw new InvalidOperationException($"Flow execution {executionId} not found");
        }

        if (execution.Status != ExecutionStatus.Paused)
        {
            _logger.LogWarning("Flow execution {ExecutionId} is not paused: {Status}",
                executionId, execution.Status);
            return;
        }

        // Remove pause state records
        var pauseStates = await _dbContext.PausedExecutions
            .Where(p => p.ExecutionId == executionId)
            .ToListAsync(cancellationToken);

        _dbContext.PausedExecutions.RemoveRange(pauseStates);

        // Update execution status
        execution.Status = ExecutionStatus.Running;
        await _dbContext.SaveChangesAsync(cancellationToken);

        // Resume execution by starting the flow again
        var success = await _flowExecutor.StartFlowAsync(executionId, cancellationToken);
        if (!success)
        {
            _logger.LogError("Failed to resume flow execution {ExecutionId}", executionId);
            throw new InvalidOperationException($"Failed to resume flow execution {executionId}");
        }

        _logger.LogInformation("Flow execution {ExecutionId} resumed successfully", executionId);
    }

    /// <summary>
    /// Cancels an execution.
    /// </summary>
    public async Task CancelAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Cancelling flow execution {ExecutionId}", executionId);

        var execution = await _dbContext.Executions
            .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

        if (execution == null)
        {
            _logger.LogError("Flow execution {ExecutionId} not found", executionId);
            throw new InvalidOperationException($"Flow execution {executionId} not found");
        }

        if (execution.Status == ExecutionStatus.Completed || execution.Status == ExecutionStatus.Failed)
        {
            _logger.LogWarning("Flow execution {ExecutionId} is already completed: {Status}",
                executionId, execution.Status);
            return;
        }

        // Update execution status
        execution.Status = ExecutionStatus.Failed;
        execution.CompletedAt = DateTime.UtcNow;

        // Cancel any running tasks
        var runningTasks = await _dbContext.TaskExecutions
            .Where(te => te.ExecutionId == executionId && te.Status == GreenFlow.Shared.TaskStatus.Running)
            .ToListAsync(cancellationToken);

        foreach (var task in runningTasks)
        {
            task.Status = GreenFlow.Shared.TaskStatus.Failed;
            task.CompletedAt = DateTime.UtcNow;
            task.ResultMessage = "Execution cancelled";
        }

        // Remove any pause states
        var pauseStates = await _dbContext.PausedExecutions
            .Where(p => p.ExecutionId == executionId)
            .ToListAsync(cancellationToken);

        _dbContext.PausedExecutions.RemoveRange(pauseStates);

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Flow execution {ExecutionId} cancelled successfully", executionId);
    }
}