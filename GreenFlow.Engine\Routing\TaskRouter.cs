using System.Text.Json;
using RabbitMQ.Client;

namespace GreenFlow.Engine.Routing
{
    public interface ITaskRouter
    {
        Task RouteAsync<T>(T message, string routingKey);
    }
    public class TaskRouter : ITaskRouter, IDisposable
    {
        private readonly IConnection _connection;
        private readonly IModel _channel;
        private readonly string _exchangeName;

        public TaskRouter(string connectionString, string exchangeName = "greenflow.tasks")
        {
            var factory = new ConnectionFactory
            {
                Uri = new Uri(connectionString)
            };
            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();
            _exchangeName = exchangeName;
            _channel.ExchangeDeclare(exchange: _exchangeName, type: ExchangeType.Direct, durable: true);
        }

        public Task RouteAsync<T>(T message, string routingKey)
        {
            var body = JsonSerializer.SerializeToUtf8Bytes(message);
            var props = _channel.CreateBasicProperties();
            props.Persistent = true;
            _channel.BasicPublish(exchange: _exchangeName,
                                  routingKey: routingKey,
                                  basicProperties: props,
                                  body: body);
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _channel?.Close();
            _connection?.Close();
        }
    }
}
