using GreenFlow.Storage.Context;
using Microsoft.EntityFrameworkCore;
using GreenFlow.Api.Endpoints;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);


builder.Services.AddOpenApi(options =>
{
    options.OpenApiVersion = Microsoft.OpenApi.OpenApiSpecVersion.OpenApi3_1;
});

// Serialize enums as strings in JSON responses
builder.Services.ConfigureHttpJsonOptions(opts =>
{
    opts.SerializerOptions.Converters.Add(new JsonStringEnumConverter());
});
// Add AutoMapper for DTO mapping
builder.Services.AddAutoMapper(typeof(GreenFlow.Api.Mapping.MappingProfile).Assembly);


builder.Services.AddDbContext<FlowDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

var app = builder.Build();

// ensure database is created/apply schema
using (var scope = app.Services.CreateScope())
{
    var db = scope.ServiceProvider.GetRequiredService<FlowDbContext>();
    db.Database.EnsureCreated();
}

app.MapOpenApi();

app.MapFlowsEndpoints();
app.MapTasksEndpoints();
app.MapTaskEdgesEndpoints();
app.MapExecutionsEndpoints();
app.MapTaskExecutionsEndpoints();
app.MapTenantsEndpoints();
app.MapPendingFormsEndpoints();
app.MapEventStreamEndpoints();

app.Run();
