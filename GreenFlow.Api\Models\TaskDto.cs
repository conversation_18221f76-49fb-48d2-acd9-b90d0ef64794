using System;
using GreenFlow.Shared;

namespace GreenFlow.Api.Models
{
    public class TaskDto
    {
        public Guid Id { get; set; }
        public Guid FlowId { get; set; }
        // Optional: include flow name for context
        public string? FlowName { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? ActionPayload { get; set; }
        public ActionType ActionType { get; set; }
        public double? RuntimeCostMultiplier { get; set; }
        public int PositionX { get; set; }
        public int PositionY { get; set; }
    }
}