namespace GreenFlow.Api.Models;

public class TenantDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int MonthlyTokenBudget { get; set; }
    public int TokensUsedThisMonth { get; set; }
    public DateTime LastReset { get; set; }
    public int RemainingTokens => MonthlyTokenBudget - TokensUsedThisMonth;
    public double UsagePercentage => MonthlyTokenBudget > 0 ? (double)TokensUsedThisMonth / MonthlyTokenBudget * 100 : 0;
}
