namespace GreenFlow.Shared;

/// <summary>
/// Defines when this edge should be followed during execution.
/// </summary>
public enum EdgeType
{
    /// <summary>
    /// Follow this edge when the source task completes successfully.
    /// </summary>
    OnSuccess,

    /// <summary>
    /// Follow this edge when the source task fails.
    /// </summary>
    OnFailure,

    /// <summary>
    /// Follow this edge after the source task has failed a specified number of times.
    /// </summary>
    OnFailureCount
}
