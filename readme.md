```
GreenFlow/
├── src/
│   ├── GreenFlow.Api/                             # Minimal API host
│   │   ├── Endpoints/                             # Route-group extensions
│   │   │   ├── FlowsEndpoints.cs
│   │   │   ├── TasksEndpoints.cs
│   │   │   ├── ExecutionsEndpoints.cs
│   │   │   ├── LookupsEndpoints.cs
│   │   │   └── EventStreamEndpoints.cs            # SSE: /api/events/{executionId}
│   │   ├── EventStreams/
│   │   │   └── FlowEventStream.cs                 # Manages SSE channels
│   │   ├── Middleware/
│   │   │   └── BudgetEnforcementMiddleware.cs     # Pre-flight token/time guard
│   │   ├── Extensions/
│   │   │   └── EndpointRouteBuilderExtensions.cs  # .MapGreenFlowEndpoints()
│   │   ├── Models/                                # DTOs, request/response types
│   │   │   └── ExecuteFlowRequest.cs
│   │   ├── Services/                              # App‑level helpers (optional)
│   │   │   └── FlowApiService.cs
│   │   └── Program.cs                             # App startup & DI
│   │
│   ├── GreenFlow.Engine/                          # Core orchestration
│   │   ├── Execution/
│   │   │   ├── ITaskFlowEngine.cs
│   │   │   ├── TaskFlowEngine.cs
│   │   │   ├── FlowExecutor.cs
│   │   │   ├── TaskContext.cs
│   │   │   └── PauseReason.cs
│   │   ├── Routing/
│   │   │   └── TaskRouter.cs
│   │   ├── Coordination/
│   │   │   └── CoordinationRuleEvaluator.cs
│   │   ├── Costing/
│   │   │   ├── ITaskCostEstimator.cs
│   │   │   ├── DefaultTaskCostEstimator.cs
│   │   │   └── TaskCostService.cs
│   │   └── Registry/
│   │       └── ITaskHandlerRegistry.cs
│   │
│   ├── GreenFlow.PluginSystem/                    # Pluggable task handlers & payloads
│   │   ├── Interfaces/
│   │   │   ├── ITaskActionHandler.cs
│   │   │   ├── ITaskPayload.cs
│   │   │   └── ITaskCostingPlugin.cs
│   │   ├── Payloads/
│   │   │   ├── FormPayload.cs
│   │   │   ├── FillPdfPayload.cs
│   │   │   ├── ChatGptPayload.cs
│   │   │   ├── CallApiPayload.cs
│   │   │   ├── CoordinationRule.cs
│   │   │   └── LoopPayload.cs
│   │   ├── Plugins/
│   │   │   ├── FillFormPlugin.cs
│   │   │   ├── FillPdfPlugin.cs
│   │   │   ├── ChatGptPlugin.cs
│   │   │   ├── CallApiPlugin.cs
│   │   │   └── SetVariablePlugin.cs
│   │   ├── Attributes/
│   │   │   └── TaskPayloadAttribute.cs
│   │   └── PayloadFactory/
│   │       └── TaskPayloadFactory.cs
│   │
│   ├── GreenFlow.Billing/                         # Token/time budgeting system
│   │   ├── TokenMeter/
│   │   │   ├── ITokenMeter.cs
│   │   │   └── TokenMeter.cs
│   │   └── UsageTracking/
│   │       ├── IRuntimeUsageTracker.cs
│   │       ├── RuntimeUsageTracker.cs
│   │       └── MonthlyBudgetResetService.cs
│   │
│   ├── GreenFlow.Storage/                         # EF Core data layer
│   │   ├── Context/
│   │   │   └── FlowDbContext.cs                   # EF DbContext with DbSets
│   │   ├── Entities/
│   │   │   ├── FlowDefinition.cs
│   │   │   ├── TaskDefinition.cs
│   │   │   ├── TaskEdge.cs
│   │   │   ├── FlowExecution.cs
│   │   │   ├── TaskExecution.cs
│   │   │   ├── PendingUserForm.cs
│   │   │   ├── Tenant.cs
│   │   │   ├── SlaConfig.cs
│   │   │   ├── TokenLedgerEntry.cs
│   │   │   └── ExecutionPauseState.cs
│   │   ├── Configuration/
│   │   │   └── ModelBuilderExtensions.cs          # Fluent API configs
│   │   └── Migrations/                            # EF migration files
│   │       ├── 20240416103000_Initial.cs
│   │       └── GreenFlowDbModelSnapshot.cs
│   │
│   ├── GreenFlow.Worker/                          # RabbitMQ background executor
│   │   ├── TaskQueueWorker.cs
│   │   └── TaskExecutionMessage.cs
│   │
│   ├── GreenFlow.Scheduler/                       # SLA & scheduled-trigger service
│   │   └── SlaMonitorService.cs
│   │
│   ├── GreenFlow.Shared/                          # Shared types & helpers
│   │   ├── ActionType.cs
│   │   ├── TaskStatus.cs
│   │   ├── ExecutionStatus.cs
│   │   ├── EdgeLabel.cs
│   │   ├── TemplateEngine.cs
│   │   └── UserIdentity.cs
│   │
│   └── GreenFlow.Web/                             # Optional SPA (Blazor or React)
│       ├── Components/
│       ├── Pages/
│       └── FormRenderer/
│
├── tests/
│   ├── GreenFlow.Engine.Tests/
│   ├── GreenFlow.PluginSystem.Tests/
│   └── GreenFlow.Api.IntegrationTests/
│
├── docker/
│   ├── docker-compose.yml
│   └── nginx.conf
│
├── deploy/
│   └── GitHubActions.yml
│
├── docs/
│   ├── architecture.md
│   └── flow-examples/
│
└── GreenFlow.sln
```