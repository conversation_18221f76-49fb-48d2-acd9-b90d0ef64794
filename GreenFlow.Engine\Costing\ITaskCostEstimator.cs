using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.Engine.Costing;

/// <summary>
/// Interface for estimating the cost of task execution.
/// </summary>
public interface ITaskCostEstimator
{
    /// <summary>
    /// Estimates the cost for executing a task with the given payload.
    /// </summary>
    /// <param name="actionType">The type of action to be performed</param>
    /// <param name="payload">The task payload</param>
    /// <returns>Estimated cost in tokens or monetary units</returns>
    Task<double> EstimateCostAsync(ActionType actionType, ITaskPayload payload);

    /// <summary>
    /// Estimates the cost for executing a task with the given payload JSON.
    /// </summary>
    /// <param name="actionType">The type of action to be performed</param>
    /// <param name="payloadJson">The task payload as JSON string</param>
    /// <returns>Estimated cost in tokens or monetary units</returns>
    Task<double> EstimateCostAsync(ActionType actionType, string payloadJson);

    /// <summary>
    /// Gets the base cost for a specific action type.
    /// </summary>
    /// <param name="actionType">The action type</param>
    /// <returns>Base cost for the action type</returns>
    double GetBaseCost(ActionType actionType);
}