namespace GreenFlow.Storage.Entities;

public class FieldRule
{
    public Guid Id { get; set; }
    /// <summary>
    /// Foreign key to the owning TaskDefinition.
    /// </summary>
    public Guid TaskId { get; set; }
    /// <summary>
    /// Navigation back to the parent task.
    /// </summary>
    public TaskDefinition Task { get; set; } = null!;
    /// <summary>
    /// Name of the field this rule applies to.
    /// </summary>
    public string FieldName { get; set; } = null!;
    /// <summary>
    /// If true, this field must be present and non-null.
    /// </summary>
    public bool Required { get; set; }
    /// <summary>
    /// If set, the minimum numeric value allowed.
    /// </summary>
    public int? Min { get; set; }
    /// <summary>
    /// If set, the maximum numeric value allowed.
    /// </summary>
    public int? Max { get; set; }
    /// <summary>
    /// Optional regex pattern the field value must match.
    /// </summary>
    public string? RegexPattern { get; set; }
    /// <summary>
    /// Optional key for an external validation routine (e.g. 'IcelandicId').
    /// </summary>
    public string? ExternalValidator { get; set; }
}