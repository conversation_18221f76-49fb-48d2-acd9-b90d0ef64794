using GreenFlow.PluginSystem.Interfaces;

namespace GreenFlow.PluginSystem.Interfaces
{
    /// <summary>
    /// Defines a plugin to estimate cost for a specific task payload type.
    /// </summary>
    /// <typeparam name="TPayload">The payload type.</typeparam>
    public interface ITaskCostingPlugin<TPayload>
        where TPayload : ITaskPayload
    {
        /// <summary>
        /// Estimates the cost for the given payload.
        /// </summary>
        /// <param name="payload">The task payload.</param>
        /// <returns>The estimated cost.</returns>
        double EstimateCost(TPayload payload);
    }
}