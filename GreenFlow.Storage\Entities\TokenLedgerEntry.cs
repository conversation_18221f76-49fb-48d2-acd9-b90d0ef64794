﻿namespace GreenFlow.Storage.Entities;
public class TokenLedgerEntry
{
    public Guid Id { get; set; }
    public Guid ExecutionId { get; set; }
    public Guid TaskId { get; set; }
    public Guid TenantId { get; set; }
    public Tenant Tenant { get; set; } = null!;
    public int TokensUsed { get; set; }
    public string? TaskType { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

