using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to fill a form based on FormPayload.
    /// </summary>
    public class FillFormPlugin : ITaskActionHandler<FormPayload>
    {
        public Task HandleAsync(FormPayload payload, IDictionary<string, object> context)
        {
            // For demonstration, store filled form in context
            context["FilledForm"] = new { payload.FormTemplate, payload.Fields };
            return Task.CompletedTask;
        }
    }
}