using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.PayloadFactory;
using GreenFlow.Shared;
using Microsoft.Extensions.Logging;

namespace GreenFlow.Engine.Costing;

/// <summary>
/// Default implementation of task cost estimator.
/// </summary>
public class DefaultTaskCostEstimator : ITaskCostEstimator
{
    private readonly ILogger<DefaultTaskCostEstimator> _logger;
    private readonly Dictionary<ActionType, double> _baseCosts;

    public DefaultTaskCostEstimator(ILogger<DefaultTaskCostEstimator> logger)
    {
        _logger = logger;
        _baseCosts = InitializeBaseCosts();
    }

    /// <summary>
    /// Estimates the cost for executing a task with the given payload.
    /// </summary>
    public async Task<double> EstimateCostAsync(ActionType actionType, ITaskPayload payload)
    {
        var baseCost = GetBaseCost(actionType);

        // Apply payload-specific cost modifiers
        var modifier = await CalculatePayloadModifierAsync(actionType, payload);

        var totalCost = baseCost * modifier;

        _logger.LogDebug("Estimated cost for {ActionType}: {Cost} (base: {BaseCost}, modifier: {Modifier})",
            actionType, totalCost, baseCost, modifier);

        return totalCost;
    }

    /// <summary>
    /// Estimates the cost for executing a task with the given payload JSON.
    /// </summary>
    public async Task<double> EstimateCostAsync(ActionType actionType, string payloadJson)
    {
        try
        {
            var payload = TaskPayloadFactory.Create(payloadJson, actionType);
            return await EstimateCostAsync(actionType, payload);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse payload for cost estimation, using base cost for {ActionType}", actionType);
            return GetBaseCost(actionType);
        }
    }

    /// <summary>
    /// Gets the base cost for a specific action type.
    /// </summary>
    public double GetBaseCost(ActionType actionType)
    {
        return _baseCosts.TryGetValue(actionType, out var cost) ? cost : 1.0;
    }

    private static Dictionary<ActionType, double> InitializeBaseCosts()
    {
        return new Dictionary<ActionType, double>
        {
            { ActionType.SetVariable, 0.1 },
            { ActionType.FillForm, 0.5 },
            { ActionType.FillPdf, 1.0 },
            { ActionType.ChatGPT, 10.0 }, // Higher cost for AI operations
            { ActionType.CallApi, 0.5 },
            { ActionType.Slack, 0.2 },
            { ActionType.SignPdf, 2.0 },
            { ActionType.Loop, 0.1 }, // Base cost per iteration
            { ActionType.HumanInput, 0.0 } // No computational cost
        };
    }

    private async Task<double> CalculatePayloadModifierAsync(ActionType actionType, ITaskPayload payload)
    {
        // Apply action-specific cost modifiers based on payload content
        return actionType switch
        {
            ActionType.ChatGPT => await CalculateChatGptModifierAsync(payload),
            ActionType.CallApi => CalculateApiModifier(payload),
            ActionType.FillPdf => CalculatePdfModifier(payload),
            ActionType.Loop => CalculateLoopModifier(payload),
            _ => 1.0
        };
    }

    private async Task<double> CalculateChatGptModifierAsync(ITaskPayload payload)
    {
        // For ChatGPT, cost depends on token count and model complexity
        // This is a simplified calculation
        await Task.CompletedTask; // Placeholder for async operations
        return 1.0; // Base modifier, could be enhanced with actual token counting
    }

    private static double CalculateApiModifier(ITaskPayload payload)
    {
        // API calls have consistent cost regardless of payload size
        return 1.0;
    }

    private static double CalculatePdfModifier(ITaskPayload payload)
    {
        // PDF operations might cost more for larger documents
        // This could be enhanced to check document size
        return 1.0;
    }

    private static double CalculateLoopModifier(ITaskPayload payload)
    {
        // Loop cost depends on the number of iterations
        // This would need access to the actual collection size
        return 1.0; // Base modifier per iteration
    }
}