using System.Collections.Concurrent;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.Engine.Registry;

/// <summary>
/// Default implementation of the task handler registry.
/// </summary>
public class TaskHandlerRegistry : ITaskHandlerRegistry
{
    private readonly ConcurrentDictionary<ActionType, object> _handlers = new();

    /// <summary>
    /// Registers a task handler for a specific action type.
    /// </summary>
    public void RegisterHandler<TPayload>(ActionType actionType, ITaskActionHandler<TPayload> handler)
        where TPayload : ITaskPayload
    {
        _handlers.AddOrUpdate(actionType, handler, (_, _) => handler);
    }

    /// <summary>
    /// Gets a task handler for the specified action type.
    /// </summary>
    public ITaskActionHandler<TPayload>? GetHandler<TPayload>(ActionType actionType)
        where TPayload : ITaskPayload
    {
        return _handlers.TryGetValue(actionType, out var handler) 
            ? handler as ITaskActionHandler<TPayload> 
            : null;
    }

    /// <summary>
    /// Gets a task handler for the specified action type (non-generic).
    /// </summary>
    public object? GetHandler(ActionType actionType)
    {
        return _handlers.TryGetValue(actionType, out var handler) ? handler : null;
    }

    /// <summary>
    /// Checks if a handler is registered for the specified action type.
    /// </summary>
    public bool HasHandler(ActionType actionType)
    {
        return _handlers.ContainsKey(actionType);
    }

    /// <summary>
    /// Gets all registered action types.
    /// </summary>
    public IEnumerable<ActionType> GetRegisteredActionTypes()
    {
        return _handlers.Keys;
    }
}
