using System;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Endpoints
{
    public static class PendingFormsEndpoints
    {
        public static void MapPendingFormsEndpoints(this WebApplication app)
        {
            var group = app.MapGroup("/api/pending-forms").WithTags("PendingForms");

            group.MapGet("/", async (FlowDbContext db) =>
                await db.PendingForms.ToListAsync())
                .WithName("GetPendingForms");

            group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db) =>
                await db.PendingForms.FindAsync(id) is PendingUserForm form
                    ? Results.Ok(form)
                    : Results.NotFound())
                .WithName("GetPendingFormById");

            group.MapPost("/", async (PendingUserForm form, FlowDbContext db) =>
            {
                form.Id = Guid.NewGuid();
                db.PendingForms.Add(form);
                await db.SaveChangesAsync();
                return Results.Created($"/api/pending-forms/{form.Id}", form);
            })
            .WithName("CreatePendingForm");

            group.MapPost("/{id:guid}/complete", async (Guid id, FlowDbContext db) =>
            {
                var form = await db.PendingForms.FindAsync(id);
                if (form is null)
                    return Results.NotFound();

                if (form.Completed)
                    return Results.BadRequest("Form is already completed.");

                form.Completed = true;
                db.PendingForms.Update(form);

                // Resume execution if it was paused for this form
                var paused = await db.PausedExecutions
                    .Where(p => p.ExecutionId == form.ExecutionId && p.TaskId == form.TaskId)
                    .ToListAsync();
                if (paused.Any())
                    db.PausedExecutions.RemoveRange(paused);

                var exec = await db.Executions.FindAsync(form.ExecutionId);
                if (exec is not null && exec.Status == ExecutionStatus.Paused)
                    exec.Status = ExecutionStatus.Running;

                await db.SaveChangesAsync();
                return Results.NoContent();
            })
            .WithName("CompletePendingForm");
        }
    }
}