using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Endpoints;

public static class TasksEndpoints
{
    public static void MapTasksEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/tasks").WithTags("Tasks");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            // include flow for FlowName mapping
            var tasks = await db.Tasks.Include(t => t.Flow).ToListAsync();
            var dtos = mapper.Map<List<TaskDto>>(tasks);
            return Results.Ok(dtos);
        })
        .WithName("GetTasks");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var task = await db.Tasks.FindAsync(id);
            return task is not null
                ? Results.Ok(mapper.Map<TaskDto>(task))
                : Results.NotFound();
        })
        .WithName("GetTaskById");

        group.MapPost("/", async (CreateTaskDto createDto, FlowDbContext db, IMapper mapper) =>
        {
            var task = mapper.Map<TaskDefinition>(createDto);
            task.Id = Guid.NewGuid();
            db.Tasks.Add(task);
            await db.SaveChangesAsync();
            var result = mapper.Map<TaskDto>(task);
            return Results.Created($"/api/tasks/{task.Id}", result);
        })
        .WithName("CreateTask");

        // Lookup for ActionType enum
        group.MapGet("/action-types", () =>
            Results.Ok(Enum.GetValues(typeof(ActionType))))
            .WithName("GetActionTypes");
    }
}
