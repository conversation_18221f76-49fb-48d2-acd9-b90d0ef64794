using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using GreenFlow.PluginSystem.PayloadFactory;

namespace GreenFlow.Api.Endpoints;

public static class TasksEndpoints
{
    public static void MapTasksEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/tasks").WithTags("Tasks");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            // include flow for FlowName mapping
            var tasks = await db.Tasks.Include(t => t.Flow).ToListAsync();
            var dtos = mapper.Map<List<TaskDto>>(tasks);
            return Results.Ok(dtos);
        })
        .WithName("GetTasks");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var task = await db.Tasks.FindAsync(id);
            return task is not null
                ? Results.Ok(mapper.Map<TaskDto>(task))
                : Results.NotFound();
        })
        .WithName("GetTaskById");

        group.MapPost("/", async (CreateTaskDto createDto, FlowDbContext db, IMapper mapper) =>
        {
            var task = mapper.Map<TaskDefinition>(createDto);
            task.Id = Guid.NewGuid();
            db.Tasks.Add(task);
            await db.SaveChangesAsync();
            var result = mapper.Map<TaskDto>(task);
            return Results.Created($"/api/tasks/{task.Id}", result);
        })
        .WithName("CreateTask");

        group.MapPut("/{id:guid}", async (Guid id, UpdateTaskDto updateDto, FlowDbContext db, IMapper mapper) =>
        {
            var task = await db.Tasks.FindAsync(id);
            if (task is null)
                return Results.NotFound();

            mapper.Map(updateDto, task);
            await db.SaveChangesAsync();
            return Results.Ok(mapper.Map<TaskDto>(task));
        })
        .WithName("UpdateTask");

        group.MapDelete("/{id:guid}", async (Guid id, FlowDbContext db) =>
        {
            var task = await db.Tasks.FindAsync(id);
            if (task is null)
                return Results.NotFound();

            db.Tasks.Remove(task);
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("DeleteTask");

        // Get tasks by flow
        group.MapGet("/flow/{flowId:guid}", async (Guid flowId, FlowDbContext db, IMapper mapper) =>
        {
            var tasks = await db.Tasks
                .Include(t => t.Flow)
                .Where(t => t.FlowId == flowId)
                .ToListAsync();
            var dtos = mapper.Map<List<TaskDto>>(tasks);
            return Results.Ok(dtos);
        })
        .WithName("GetTasksByFlow");

        // Validate task payload
        group.MapPost("/{id:guid}/validate-payload", async (Guid id, FlowDbContext db) =>
        {
            var task = await db.Tasks.FindAsync(id);
            if (task is null)
                return Results.NotFound();

            try
            {
                // Try to create payload to validate JSON structure
                if (!string.IsNullOrEmpty(task.ActionPayload))
                {
                    var payload = TaskPayloadFactory.Create(task.ActionPayload, task.ActionType);
                    return Results.Ok(new { IsValid = true, Message = "Payload is valid" });
                }
                else
                {
                    return Results.Ok(new { IsValid = true, Message = "No payload to validate" });
                }
            }
            catch (Exception ex)
            {
                return Results.Ok(new { IsValid = false, Message = ex.Message });
            }
        })
        .WithName("ValidateTaskPayload");

        // Lookup for ActionType enum
        group.MapGet("/action-types", () =>
            Results.Ok(Enum.GetValues(typeof(ActionType))))
            .WithName("GetActionTypes");
    }
}
