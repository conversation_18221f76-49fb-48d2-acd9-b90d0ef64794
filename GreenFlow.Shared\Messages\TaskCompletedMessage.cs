namespace GreenFlow.Shared.Messages;

/// <summary>
/// Message sent when a task execution completes (success or failure).
/// </summary>
public class TaskCompletedMessage
{
    public Guid ExecutionId { get; set; }
    public Guid TaskId { get; set; }
    public TaskStatus Status { get; set; }
    public string? ResultMessage { get; set; }
    public Dictionary<string, object> OutputData { get; set; } = new();
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan ExecutionDuration { get; set; }
    public double? CostIncurred { get; set; }
}
