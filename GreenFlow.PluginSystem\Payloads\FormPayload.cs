using System.Collections.Generic;
using GreenFlow.PluginSystem.Interfaces;
// Removed attribute-based mapping; using instance property instead
using GreenFlow.Storage.Entities;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for FillForm tasks.
    /// </summary>
    public class FormPayload : ITaskPayload
    {
        /// <summary>
        /// The identifier or template name of the form to fill.
        /// </summary>
        public string FormTemplate { get; set; } = null!;

        /// <summary>
        /// Field values to populate in the form.
        /// </summary>
        public Dictionary<string, object> Fields { get; set; } = new();
        /// <inheritdoc />
        public ActionType ActionType => ActionType.FillForm;
    }
}