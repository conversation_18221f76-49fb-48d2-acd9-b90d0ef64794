using GreenFlow.Shared;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GreenFlow.Engine.Costing;

/// <summary>
/// Service for managing task costs and budget tracking.
/// </summary>
public class TaskCostService
{
    private readonly FlowDbContext _dbContext;
    private readonly ITaskCostEstimator _costEstimator;
    private readonly ILogger<TaskCostService> _logger;

    public TaskCostService(
        FlowDbContext dbContext,
        ITaskCostEstimator costEstimator,
        ILogger<TaskCostService> logger)
    {
        _dbContext = dbContext;
        _costEstimator = costEstimator;
        _logger = logger;
    }

    /// <summary>
    /// Estimates the total cost for a flow execution.
    /// </summary>
    public async Task<double> EstimateFlowCostAsync(Guid flowId, CancellationToken cancellationToken = default)
    {
        var flow = await _dbContext.Flows
            .Include(f => f.Tasks)
            .FirstOrDefaultAsync(f => f.Id == flowId, cancellationToken);

        if (flow == null)
        {
            throw new InvalidOperationException($"Flow {flowId} not found");
        }

        double totalCost = 0;
        foreach (var task in flow.Tasks)
        {
            var taskCost = await _costEstimator.EstimateCostAsync(task.ActionType, task.ActionPayload ?? "{}");

            // Apply runtime cost multiplier if specified
            if (task.RuntimeCostMultiplier.HasValue)
            {
                taskCost *= task.RuntimeCostMultiplier.Value;
            }

            totalCost += taskCost;
        }

        _logger.LogInformation("Estimated total cost for flow {FlowId}: {TotalCost}", flowId, totalCost);
        return totalCost;
    }

    /// <summary>
    /// Records the actual cost incurred by a task execution.
    /// </summary>
    public async Task RecordTaskCostAsync(Guid executionId, Guid taskId, double cost, CancellationToken cancellationToken = default)
    {
        var execution = await _dbContext.Executions
            .Include(e => e.Flow)
            .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

        if (execution == null)
        {
            _logger.LogWarning("Execution {ExecutionId} not found for cost recording", executionId);
            return;
        }

        var ledgerEntry = new TokenLedgerEntry
        {
            Id = Guid.NewGuid(),
            ExecutionId = executionId,
            TaskId = taskId,
            TenantId = execution.Flow.TenantId,
            TokensUsed = (int)Math.Ceiling(cost), // Convert cost to tokens
            TaskType = taskId.ToString(),
            Timestamp = DateTime.UtcNow
        };

        _dbContext.TokenLedger.Add(ledgerEntry);
        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Recorded cost {Cost} for task {TaskId} in execution {ExecutionId}",
            cost, taskId, executionId);
    }

    /// <summary>
    /// Checks if a tenant has sufficient budget for a flow execution.
    /// </summary>
    public async Task<bool> CheckBudgetAsync(Guid tenantId, double estimatedCost, CancellationToken cancellationToken = default)
    {
        var tenant = await _dbContext.Tenants.FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);
        if (tenant == null)
        {
            _logger.LogWarning("Tenant {TenantId} not found for budget check", tenantId);
            return false;
        }

        var remainingBudget = tenant.MonthlyTokenBudget - tenant.TokensUsedThisMonth;
        var hasEnoughBudget = remainingBudget >= estimatedCost;

        _logger.LogDebug("Budget check for tenant {TenantId}: remaining {Remaining}, needed {Needed}, sufficient: {Sufficient}",
            tenantId, remainingBudget, estimatedCost, hasEnoughBudget);

        return hasEnoughBudget;
    }
}