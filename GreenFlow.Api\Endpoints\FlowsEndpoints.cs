using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Endpoints;

public static class FlowsEndpoints
{
    public static void MapFlowsEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/flows").WithTags("Flows");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            var flows = await db.Flows.Include(f => f.Tasks).Include(f => f.Edges).ToListAsync();
            var dtos = mapper.Map<List<FlowDto>>(flows);
            return Results.Ok(dtos);
        })
        .WithName("GetFlows");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var flow = await db.Flows.Include(f => f.Tasks).Include(f => f.Edges)
                .FirstOrDefaultAsync(f => f.Id == id);
            return flow is not null
                ? Results.Ok(mapper.Map<FlowDto>(flow))
                : Results.NotFound();
        })
        .WithName("GetFlowById");

        group.MapPost("/", async (CreateFlowDto createDto, FlowDbContext db, IMapper mapper) =>
        {
            var flow = mapper.Map<FlowDefinition>(createDto);
            flow.Id = Guid.NewGuid();
            db.Flows.Add(flow);
            await db.SaveChangesAsync();
            var result = mapper.Map<FlowDto>(flow);
            return Results.Created($"/api/flows/{flow.Id}", result);
        })
        .WithName("CreateFlow");
    }
}
