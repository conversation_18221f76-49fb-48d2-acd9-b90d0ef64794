using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Endpoints;

public static class FlowsEndpoints
{
    public static void MapFlowsEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/flows").WithTags("Flows");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            var flows = await db.Flows.Include(f => f.Tasks).Include(f => f.Edges).ToListAsync();
            var dtos = mapper.Map<List<FlowDto>>(flows);
            return Results.Ok(dtos);
        })
        .WithName("GetFlows");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var flow = await db.Flows.Include(f => f.Tasks).Include(f => f.Edges)
                .FirstOrDefaultAsync(f => f.Id == id);
            return flow is not null
                ? Results.Ok(mapper.Map<FlowDto>(flow))
                : Results.NotFound();
        })
        .WithName("GetFlowById");

        group.MapPost("/", async (CreateFlowDto createDto, FlowDbContext db, IMapper mapper) =>
        {
            var flow = mapper.Map<FlowDefinition>(createDto);
            flow.Id = Guid.NewGuid();
            db.Flows.Add(flow);
            await db.SaveChangesAsync();
            var result = mapper.Map<FlowDto>(flow);
            return Results.Created($"/api/flows/{flow.Id}", result);
        })
        .WithName("CreateFlow");

        group.MapPut("/{id:guid}", async (Guid id, UpdateFlowDto updateDto, FlowDbContext db, IMapper mapper) =>
        {
            var flow = await db.Flows.FindAsync(id);
            if (flow is null)
                return Results.NotFound();

            mapper.Map(updateDto, flow);
            await db.SaveChangesAsync();
            return Results.Ok(mapper.Map<FlowDto>(flow));
        })
        .WithName("UpdateFlow");

        group.MapDelete("/{id:guid}", async (Guid id, FlowDbContext db) =>
        {
            var flow = await db.Flows.FindAsync(id);
            if (flow is null)
                return Results.NotFound();

            db.Flows.Remove(flow);
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("DeleteFlow");

        // Execute a flow (create a new execution)
        group.MapPost("/{id:guid}/execute", async (Guid id, ExecuteFlowRequest request, FlowDbContext db) =>
        {
            var flow = await db.Flows.FindAsync(id);
            if (flow is null)
                return Results.NotFound();

            var execution = new FlowExecution
            {
                Id = Guid.NewGuid(),
                FlowId = id,
                Status = ExecutionStatus.Running,
                StartedAt = DateTime.UtcNow,
                Input = request.Input ?? new Dictionary<string, object>(),
                Context = request.Input ?? new Dictionary<string, object>()
            };

            db.Executions.Add(execution);
            await db.SaveChangesAsync();

            return Results.Created($"/api/executions/{execution.Id}", execution);
        })
        .WithName("ExecuteFlow");

        // Get flows by tenant
        group.MapGet("/tenant/{tenantId:guid}", async (Guid tenantId, FlowDbContext db, IMapper mapper) =>
        {
            var flows = await db.Flows
                .Include(f => f.Tasks)
                .Include(f => f.Edges)
                .Where(f => f.TenantId == tenantId)
                .ToListAsync();
            var dtos = mapper.Map<List<FlowDto>>(flows);
            return Results.Ok(dtos);
        })
        .WithName("GetFlowsByTenant");

        // Validate a flow definition
        group.MapPost("/{id:guid}/validate", async (Guid id, FlowDbContext db) =>
        {
            var flow = await db.Flows
                .Include(f => f.Tasks)
                .Include(f => f.Edges)
                .FirstOrDefaultAsync(f => f.Id == id);

            if (flow is null)
                return Results.NotFound();

            var validation = ValidateFlow(flow);
            return Results.Ok(validation);
        })
        .WithName("ValidateFlow");
    }

    private static object ValidateFlow(FlowDefinition flow)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // Check if flow has tasks
        if (!flow.Tasks.Any())
        {
            errors.Add("Flow must have at least one task");
        }

        // Check if start task is defined and exists
        if (flow.StartTaskId == null)
        {
            errors.Add("Flow must have a start task defined");
        }
        else if (!flow.Tasks.Any(t => t.Id == flow.StartTaskId))
        {
            errors.Add("Start task does not exist in flow");
        }

        // Check for orphaned tasks (tasks with no incoming edges except start task)
        var tasksWithIncomingEdges = flow.Edges.Select(e => e.ToTaskId).ToHashSet();
        tasksWithIncomingEdges.Add(flow.StartTaskId ?? Guid.Empty);

        var orphanedTasks = flow.Tasks.Where(t => !tasksWithIncomingEdges.Contains(t.Id)).ToList();
        foreach (var task in orphanedTasks)
        {
            warnings.Add($"Task '{task.Name}' has no incoming edges and is not the start task");
        }

        // Check for tasks with no outgoing edges (potential end tasks)
        var tasksWithOutgoingEdges = flow.Edges.Select(e => e.FromTaskId).ToHashSet();
        var endTasks = flow.Tasks.Where(t => !tasksWithOutgoingEdges.Contains(t.Id)).ToList();

        if (!endTasks.Any())
        {
            warnings.Add("Flow has no end tasks (tasks with no outgoing edges)");
        }

        return new
        {
            IsValid = !errors.Any(),
            Errors = errors,
            Warnings = warnings,
            TaskCount = flow.Tasks.Count,
            EdgeCount = flow.Edges.Count,
            EndTasks = endTasks.Select(t => new { t.Id, t.Name }).ToList()
        };
    }
}
