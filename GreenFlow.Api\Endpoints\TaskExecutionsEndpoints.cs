using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using GreenFlow.Shared;

namespace GreenFlow.Api.Endpoints;

public static class TaskExecutionsEndpoints
{
    public static void MapTaskExecutionsEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/task-executions").WithTags("TaskExecutions");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            var executions = await db.TaskExecutions
                .Include(te => te.Task)
                .Include(te => te.Execution)
                .ToListAsync();
            var dtos = mapper.Map<List<TaskExecutionDto>>(executions);
            return Results.Ok(dtos);
        })
        .WithName("GetTaskExecutions");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var execution = await db.TaskExecutions
                .Include(te => te.Task)
                .Include(te => te.Execution)
                .FirstOrDefaultAsync(te => te.Id == id);
            return execution is not null
                ? Results.Ok(mapper.Map<TaskExecutionDto>(execution))
                : Results.NotFound();
        })
        .WithName("GetTaskExecutionById");

        // Get task executions for a specific flow execution
        group.MapGet("/execution/{executionId:guid}", async (Guid executionId, FlowDbContext db, IMapper mapper) =>
        {
            var executions = await db.TaskExecutions
                .Include(te => te.Task)
                .Where(te => te.ExecutionId == executionId)
                .ToListAsync();
            var dtos = mapper.Map<List<TaskExecutionDto>>(executions);
            return Results.Ok(dtos);
        })
        .WithName("GetTaskExecutionsByExecution");

        // Get task executions for a specific task
        group.MapGet("/task/{taskId:guid}", async (Guid taskId, FlowDbContext db, IMapper mapper) =>
        {
            var executions = await db.TaskExecutions
                .Include(te => te.Execution)
                .Where(te => te.TaskId == taskId)
                .ToListAsync();
            var dtos = mapper.Map<List<TaskExecutionDto>>(executions);
            return Results.Ok(dtos);
        })
        .WithName("GetTaskExecutionsByTask");

        // Retry a failed task execution
        group.MapPost("/{id:guid}/retry", async (Guid id, FlowDbContext db) =>
        {
            var execution = await db.TaskExecutions.FindAsync(id);
            if (execution is null)
                return Results.NotFound();

            if (execution.Status != TaskStatus.Failed)
                return Results.BadRequest("Only failed task executions can be retried");

            execution.Status = TaskStatus.NotStarted;
            execution.ResultMessage = null;
            execution.CompletedAt = null;
            
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("RetryTaskExecution");

        // Lookup for TaskStatus enum
        group.MapGet("/statuses", () =>
            Results.Ok(Enum.GetValues(typeof(TaskStatus))))
            .WithName("GetTaskStatuses");
    }
}
