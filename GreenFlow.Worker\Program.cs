using GreenFlow.Worker;
using GreenFlow.Engine.Registry;
using GreenFlow.PluginSystem.Plugins;
using GreenFlow.PluginSystem.Payloads;
using GreenFlow.Shared;

var builder = Host.CreateApplicationBuilder(args);

// Register the task handler registry
builder.Services.AddSingleton<ITaskHandlerRegistry, TaskHandlerRegistry>();

// Register HTTP client for API calls
builder.Services.AddHttpClient<CallApiPlugin>();

// Register all the plugins
builder.Services.AddSingleton<SetVariablePlugin>();
builder.Services.AddSingleton<FillFormPlugin>();
builder.Services.AddSingleton<FillPdfPlugin>();
builder.Services.AddSingleton<ChatGptPlugin>();
builder.Services.AddSingleton<CallApiPlugin>();
builder.Services.AddSingleton<SlackPlugin>();
builder.Services.AddSingleton<SignPdfPlugin>();
builder.Services.AddSingleton<HumanInputPlugin>();
builder.Services.AddSingleton<LoopPlugin>();

// Register the worker service
builder.Services.AddHostedService<TaskQueueWorker>();

var host = builder.Build();

// Register all plugins with the handler registry
var handlerRegistry = host.Services.GetRequiredService<ITaskHandlerRegistry>();
var serviceProvider = host.Services;

handlerRegistry.RegisterHandler(ActionType.SetVariable, serviceProvider.GetRequiredService<SetVariablePlugin>());
handlerRegistry.RegisterHandler(ActionType.FillForm, serviceProvider.GetRequiredService<FillFormPlugin>());
handlerRegistry.RegisterHandler(ActionType.FillPdf, serviceProvider.GetRequiredService<FillPdfPlugin>());
handlerRegistry.RegisterHandler(ActionType.ChatGPT, serviceProvider.GetRequiredService<ChatGptPlugin>());
handlerRegistry.RegisterHandler(ActionType.CallApi, serviceProvider.GetRequiredService<CallApiPlugin>());
handlerRegistry.RegisterHandler(ActionType.Slack, serviceProvider.GetRequiredService<SlackPlugin>());
handlerRegistry.RegisterHandler(ActionType.SignPdf, serviceProvider.GetRequiredService<SignPdfPlugin>());
handlerRegistry.RegisterHandler(ActionType.HumanInput, serviceProvider.GetRequiredService<HumanInputPlugin>());
handlerRegistry.RegisterHandler(ActionType.Loop, serviceProvider.GetRequiredService<LoopPlugin>());

host.Run();
