using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using System.Linq;
using System;

namespace GreenFlow.Api.Endpoints;

public static class ExecutionsEndpoints
{
    public static void MapExecutionsEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/executions").WithTags("Executions");

        group.MapGet("/", async (FlowDbContext db) =>
            await db.Executions.ToListAsync())
            .WithName("GetExecutions");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db) =>
            await db.Executions.FindAsync(id) is FlowExecution exec
                ? Results.Ok(exec)
                : Results.NotFound())
            .WithName("GetExecutionById");

        group.MapPost("/", async (FlowExecution execution, FlowDbContext db) =>
        {
            execution.Id = Guid.NewGuid();
            db.Executions.Add(execution);
            await db.SaveChangesAsync();
            return Results.Created($"/api/executions/{execution.Id}", execution);
        })
        .WithName("CreateExecution");

        // Lookup for ExecutionStatus enum
        group.MapGet("/statuses", () =>
            Results.Ok(Enum.GetValues(typeof(ExecutionStatus))))
            .WithName("GetExecutionStatuses");

        // Resume a paused execution
        group.MapPost("/{id:guid}/resume", async (Guid id, FlowDbContext db) =>
        {
            var paused = await db.PausedExecutions.Where(p => p.ExecutionId == id).ToListAsync();
            if (!paused.Any())
                return Results.BadRequest("Execution is not paused or no pause state found.");
            db.PausedExecutions.RemoveRange(paused);
            var exec = await db.Executions.FindAsync(id);
            if (exec is not null && exec.Status == ExecutionStatus.Paused)
                exec.Status = ExecutionStatus.Running;
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("ResumeExecution");
    }
}
