using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using System.Linq;
using System;

namespace GreenFlow.Api.Endpoints;

public static class ExecutionsEndpoints
{
    public static void MapExecutionsEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/executions").WithTags("Executions");

        group.MapGet("/", async (FlowDbContext db) =>
            await db.Executions.ToListAsync())
            .WithName("GetExecutions");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db) =>
            await db.Executions.FindAsync(id) is FlowExecution exec
                ? Results.Ok(exec)
                : Results.NotFound())
            .WithName("GetExecutionById");

        group.MapPost("/", async (FlowExecution execution, FlowDbContext db) =>
        {
            execution.Id = Guid.NewGuid();
            db.Executions.Add(execution);
            await db.SaveChangesAsync();
            return Results.Created($"/api/executions/{execution.Id}", execution);
        })
        .WithName("CreateExecution");

        // Lookup for ExecutionStatus enum
        group.MapGet("/statuses", () =>
            Results.Ok(Enum.GetValues(typeof(ExecutionStatus))))
            .WithName("GetExecutionStatuses");

        // Resume a paused execution
        group.MapPost("/{id:guid}/resume", async (Guid id, FlowDbContext db) =>
        {
            var paused = await db.PausedExecutions.Where(p => p.ExecutionId == id).ToListAsync();
            if (!paused.Any())
                return Results.BadRequest("Execution is not paused or no pause state found.");
            db.PausedExecutions.RemoveRange(paused);
            var exec = await db.Executions.FindAsync(id);
            if (exec is not null && exec.Status == ExecutionStatus.Paused)
                exec.Status = ExecutionStatus.Running;
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("ResumeExecution");

        // Cancel an execution
        group.MapPost("/{id:guid}/cancel", async (Guid id, FlowDbContext db) =>
        {
            var exec = await db.Executions.FindAsync(id);
            if (exec is null)
                return Results.NotFound();

            if (exec.Status == ExecutionStatus.Completed || exec.Status == ExecutionStatus.Failed)
                return Results.BadRequest("Cannot cancel a completed or failed execution");

            exec.Status = ExecutionStatus.Failed;
            exec.CompletedAt = DateTime.UtcNow;

            // Cancel running tasks
            var runningTasks = await db.TaskExecutions
                .Where(te => te.ExecutionId == id && te.Status == TaskStatus.Running)
                .ToListAsync();

            foreach (var task in runningTasks)
            {
                task.Status = TaskStatus.Failed;
                task.CompletedAt = DateTime.UtcNow;
                task.ResultMessage = "Execution cancelled";
            }

            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("CancelExecution");

        // Start/trigger execution of a flow
        group.MapPost("/{id:guid}/start", async (Guid id, FlowDbContext db) =>
        {
            var exec = await db.Executions.FindAsync(id);
            if (exec is null)
                return Results.NotFound();

            if (exec.Status != ExecutionStatus.Running)
                return Results.BadRequest("Execution must be in Running status to start");

            // TODO: Integrate with TaskFlowEngine to actually start the execution
            // For now, just return success
            return Results.NoContent();
        })
        .WithName("StartExecution");

        // Get executions for a specific flow
        group.MapGet("/flow/{flowId:guid}", async (Guid flowId, FlowDbContext db) =>
        {
            var executions = await db.Executions
                .Where(e => e.FlowId == flowId)
                .OrderByDescending(e => e.StartedAt)
                .ToListAsync();
            return Results.Ok(executions);
        })
        .WithName("GetExecutionsByFlow");
    }
}
