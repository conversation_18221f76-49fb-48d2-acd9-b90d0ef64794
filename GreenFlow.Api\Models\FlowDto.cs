using System;
using System.Collections.Generic;

namespace GreenFlow.Api.Models
{
    public class FlowDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public IEnumerable<TaskDto> Tasks { get; set; } = new List<TaskDto>();
        public IEnumerable<TaskEdgeDto> Edges { get; set; } = new List<TaskEdgeDto>();
    }
}