// CoordinationRule payload is used by coordination subsystem, not part of action payloads

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload representing a coordination rule for subtasks.
    /// </summary>
public class CoordinationRule
    {
        /// <summary>
        /// The rule expression or identifier.
        /// </summary>
        public string RuleExpression { get; set; } = null!;
    }
}