using System;
using System.Threading;
using System.Threading.Tasks;

namespace GreenFlow.Engine.Execution
{
    /// <summary>
    /// Public API for orchestrating task flow execution.
    /// </summary>
    public interface ITaskFlowEngine
    {
        /// <summary>
        /// Starts a flow execution by its identifier.
        /// </summary>
        Task StartAsync(Guid executionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Resumes a paused execution.
        /// </summary>
        Task ResumeAsync(Guid executionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancels an execution.
        /// </summary>
        Task CancelAsync(Guid executionId, CancellationToken cancellationToken = default);
    }
}
