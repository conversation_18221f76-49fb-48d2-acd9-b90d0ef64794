using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Storage.Entities;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for CallApi tasks.
    /// </summary>
    public class CallApiPayload : ITaskPayload
    {
        /// <summary>
        /// The HTTP method (GET, POST, etc.).
        /// </summary>
        public string Method { get; set; } = "GET";

        /// <summary>
        /// The request URL.
        /// </summary>
        public string Url { get; set; } = null!;

        /// <summary>
        /// Optional headers.
        /// </summary>
        public Dictionary<string, string>? Headers { get; set; }

        /// <summary>
        /// Optional request body.
        /// </summary>
        public object? Body { get; set; }
        /// <inheritdoc />
        public ActionType ActionType => ActionType.CallApi;
    }
}