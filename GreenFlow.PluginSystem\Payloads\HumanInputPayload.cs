using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for human input tasks that require user interaction.
    /// </summary>
    public class HumanInputPayload : ITaskPayload
    {
        /// <summary>
        /// The title of the form or input request.
        /// </summary>
        public string Title { get; set; } = null!;

        /// <summary>
        /// Description or instructions for the user.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// The user or role assigned to complete this task.
        /// </summary>
        public string? Assignee { get; set; }

        /// <summary>
        /// Form fields that the user needs to fill out.
        /// </summary>
        public List<FormField> Fields { get; set; } = new();

        /// <summary>
        /// Due date for the human input task.
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Priority level of the task.
        /// </summary>
        public TaskPriority Priority { get; set; } = TaskPriority.Normal;

        /// <inheritdoc />
        public ActionType ActionType => ActionType.HumanInput;
    }

    /// <summary>
    /// Represents a form field for human input.
    /// </summary>
    public class FormField
    {
        public string Name { get; set; } = null!;
        public string Label { get; set; } = null!;
        public FormFieldType Type { get; set; } = FormFieldType.Text;
        public bool Required { get; set; } = false;
        public string? DefaultValue { get; set; }
        public string? Placeholder { get; set; }
        public List<string>? Options { get; set; } // For select/radio fields
        public Dictionary<string, object>? Validation { get; set; }
    }

    /// <summary>
    /// Types of form fields.
    /// </summary>
    public enum FormFieldType
    {
        Text,
        TextArea,
        Number,
        Email,
        Date,
        DateTime,
        Select,
        Radio,
        Checkbox,
        File
    }

    /// <summary>
    /// Priority levels for human input tasks.
    /// </summary>
    public enum TaskPriority
    {
        Low,
        Normal,
        High,
        Urgent
    }
}
