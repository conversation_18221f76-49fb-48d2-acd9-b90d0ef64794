namespace GreenFlow.Shared.Messages;

/// <summary>
/// Message sent when a flow execution completes.
/// </summary>
public class FlowExecutionCompletedMessage
{
    public Guid ExecutionId { get; set; }
    public Guid FlowId { get; set; }
    public string FlowName { get; set; } = string.Empty;
    public ExecutionStatus Status { get; set; }
    public Dictionary<string, object> FinalContext { get; set; } = new();
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan TotalDuration { get; set; }
    public double? TotalCost { get; set; }
    public string? ErrorMessage { get; set; }
}
