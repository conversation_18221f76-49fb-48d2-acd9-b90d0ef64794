using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to handle loop iterations over collections.
    /// </summary>
    public class LoopPlugin : ITaskActionHandler<LoopPayload>
    {
        public Task HandleAsync(LoopPayload payload, IDictionary<string, object> context)
        {
            // Get the collection from the context using the specified path
            if (!TryGetValueFromPath(context, payload.CollectionPath, out var collectionValue))
            {
                throw new InvalidOperationException($"Collection not found at path: {payload.CollectionPath}");
            }

            // Convert to enumerable
            if (collectionValue is not IEnumerable<object> collection)
            {
                throw new InvalidOperationException($"Value at path {payload.CollectionPath} is not a collection");
            }

            // Store loop information in context
            var loopInfo = new
            {
                CollectionPath = payload.CollectionPath,
                IteratorName = payload.IteratorName,
                Items = collection.ToList(),
                CurrentIndex = 0,
                TotalCount = collection.Count()
            };

            context["LoopInfo"] = loopInfo;

            // In a real implementation, this would:
            // 1. Create subtasks for each item in the collection
            // 2. Set up the iterator variable for each subtask
            // 3. Coordinate the execution of all subtasks

            return Task.CompletedTask;
        }

        private static bool TryGetValueFromPath(IDictionary<string, object> context, string path, out object? value)
        {
            value = null;
            var parts = path.Split('.');
            object current = context;

            foreach (var part in parts)
            {
                if (current is IDictionary<string, object> dict && dict.TryGetValue(part, out var nextValue))
                {
                    current = nextValue;
                }
                else
                {
                    return false;
                }
            }

            value = current;
            return true;
        }
    }
}
