using System.Text;
using System.Text.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using GreenFlow.Shared.Messages;

namespace GreenFlow.Worker
{
    /// <summary>
    /// Background service that consumes task execution messages from RabbitMQ.
    /// </summary>
    public class TaskQueueWorker : BackgroundService
    {
        private readonly ILogger<TaskQueueWorker> _logger;
        private readonly IConfiguration _configuration;
        private IConnection? _connection;
        private IModel? _channel;

        public TaskQueueWorker(ILogger<TaskQueueWorker> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var rabbitMqUri = _configuration["RabbitMq:Uri"];
            if (string.IsNullOrWhiteSpace(rabbitMqUri))
            {
                _logger.LogError("RabbitMq:Uri configuration is missing.");
                return;
            }

            var exchangeName = _configuration["RabbitMq:ExchangeName"] ?? "greenflow.tasks";
            var queueName = _configuration["RabbitMq:QueueName"] ?? "greenflow.tasks.queue";
            var routingKey = _configuration["RabbitMq:RoutingKey"] ?? string.Empty;

            var factory = new ConnectionFactory { Uri = new Uri(rabbitMqUri) };
            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();

            _channel.ExchangeDeclare(exchange: exchangeName, type: ExchangeType.Direct, durable: true);
            _channel.QueueDeclare(queue: queueName, durable: true, exclusive: false, autoDelete: false, arguments: null);
            _channel.QueueBind(queue: queueName, exchange: exchangeName, routingKey: routingKey);

            var consumer = new AsyncEventingBasicConsumer(_channel);
            consumer.Received += async (sender, ea) =>
            {
                try
                {
                    var messageJson = Encoding.UTF8.GetString(ea.Body.ToArray());
                    var message = JsonSerializer.Deserialize<TaskExecutionMessage>(messageJson);
                    if (message == null)
                        throw new InvalidOperationException("Received null TaskExecutionMessage.");

                    _logger.LogInformation("Received TaskExecutionMessage: ExecutionId={ExecutionId}, TaskId={TaskId}",
                        message.ExecutionId, message.TaskId);

                    // TODO: Execute the task via engine or plugin handler

                    _channel.BasicAck(ea.DeliveryTag, multiple: false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing TaskExecutionMessage.");
                    if (_channel != null)
                        _channel.BasicNack(ea.DeliveryTag, multiple: false, requeue: false);
                }
            };

            _channel.BasicConsume(queue: queueName, autoAck: false, consumer: consumer);
            _logger.LogInformation("TaskQueueWorker listening on queue '{QueueName}'...", queueName);

            try
            {
                await Task.Delay(Timeout.Infinite, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                // Shutdown requested
            }
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping TaskQueueWorker...");
            _channel?.Close();
            _connection?.Close();
            return base.StopAsync(cancellationToken);
        }
    }
}