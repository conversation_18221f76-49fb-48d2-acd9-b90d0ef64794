using System.Text;
using System.Text.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using GreenFlow.Shared.Messages;
using GreenFlow.Shared;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.PayloadFactory;
using GreenFlow.Engine.Registry;

namespace GreenFlow.Worker
{
    /// <summary>
    /// Background service that consumes task execution messages from RabbitMQ.
    /// </summary>
    public class TaskQueueWorker : BackgroundService
    {
        private readonly ILogger<TaskQueueWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly ITaskHandlerRegistry _handlerRegistry;
        private IConnection? _connection;
        private IModel? _channel;

        public TaskQueueWorker(
            ILogger<TaskQueueWorker> logger,
            IConfiguration configuration,
            ITaskHandlerRegistry handlerRegistry)
        {
            _logger = logger;
            _configuration = configuration;
            _handlerRegistry = handlerRegistry;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var rabbitMqUri = _configuration["RabbitMq:Uri"];
            if (string.IsNullOrWhiteSpace(rabbitMqUri))
            {
                _logger.LogError("RabbitMq:Uri configuration is missing.");
                return;
            }

            var exchangeName = _configuration["RabbitMq:ExchangeName"] ?? "greenflow.tasks";
            var queueName = _configuration["RabbitMq:QueueName"] ?? "greenflow.tasks.queue";
            var routingKey = _configuration["RabbitMq:RoutingKey"] ?? string.Empty;

            var factory = new ConnectionFactory { Uri = new Uri(rabbitMqUri) };
            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();

            _channel.ExchangeDeclare(exchange: exchangeName, type: ExchangeType.Direct, durable: true);
            _channel.QueueDeclare(queue: queueName, durable: true, exclusive: false, autoDelete: false, arguments: null);
            _channel.QueueBind(queue: queueName, exchange: exchangeName, routingKey: routingKey);

            var consumer = new AsyncEventingBasicConsumer(_channel);
            consumer.Received += async (sender, ea) =>
            {
                try
                {
                    var messageJson = Encoding.UTF8.GetString(ea.Body.ToArray());
                    var message = JsonSerializer.Deserialize<TaskExecutionMessage>(messageJson);
                    if (message == null)
                        throw new InvalidOperationException("Received null TaskExecutionMessage.");

                    _logger.LogInformation("Received TaskExecutionMessage: ExecutionId={ExecutionId}, TaskId={TaskId}",
                        message.ExecutionId, message.TaskId);

                    // Execute the task via plugin handler
                    await ExecuteTaskAsync(message);

                    _channel.BasicAck(ea.DeliveryTag, multiple: false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing TaskExecutionMessage.");
                    if (_channel != null)
                        _channel.BasicNack(ea.DeliveryTag, multiple: false, requeue: false);
                }
            };

            _channel.BasicConsume(queue: queueName, autoAck: false, consumer: consumer);
            _logger.LogInformation("TaskQueueWorker listening on queue '{QueueName}'...", queueName);

            try
            {
                await Task.Delay(Timeout.Infinite, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                // Shutdown requested
            }
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping TaskQueueWorker...");
            _channel?.Close();
            _connection?.Close();
            return base.StopAsync(cancellationToken);
        }

        private async Task ExecuteTaskAsync(TaskExecutionMessage message)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Executing task {TaskId} of type {ActionType}", message.TaskId, message.ActionType);

                // Get the appropriate handler for this action type
                var handler = _handlerRegistry.GetHandler(message.ActionType);
                if (handler == null)
                {
                    throw new InvalidOperationException($"No handler registered for action type {message.ActionType}");
                }

                // Create the payload from JSON
                var payload = TaskPayloadFactory.Create(message.ActionPayload ?? "{}", message.ActionType);

                // Execute the task using reflection to call the generic HandleAsync method
                var handlerType = handler.GetType();
                var handleMethod = handlerType.GetMethod("HandleAsync");
                if (handleMethod == null)
                {
                    throw new InvalidOperationException($"Handler {handlerType.Name} does not have HandleAsync method");
                }

                // Invoke the handler
                var task = (Task)handleMethod.Invoke(handler, new object[] { payload, message.Context })!;
                await task;

                var completedMessage = new TaskCompletedMessage
                {
                    ExecutionId = message.ExecutionId,
                    TaskId = message.TaskId,
                    Status = TaskStatus.Success,
                    OutputData = message.Context,
                    CompletedAt = DateTime.UtcNow,
                    ExecutionDuration = DateTime.UtcNow - startTime
                };

                // TODO: Send completion message back to the engine
                _logger.LogInformation("Task {TaskId} completed successfully", message.TaskId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Task {TaskId} failed: {Error}", message.TaskId, ex.Message);

                var failedMessage = new TaskCompletedMessage
                {
                    ExecutionId = message.ExecutionId,
                    TaskId = message.TaskId,
                    Status = TaskStatus.Failed,
                    ResultMessage = ex.Message,
                    CompletedAt = DateTime.UtcNow,
                    ExecutionDuration = DateTime.UtcNow - startTime
                };

                // TODO: Send failure message back to the engine
            }
        }
    }
}