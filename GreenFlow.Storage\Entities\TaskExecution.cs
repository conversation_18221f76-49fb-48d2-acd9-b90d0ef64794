﻿using GreenFlow.Shared;

namespace GreenFlow.Storage.Entities;

public class TaskExecution
{
    public Guid Id { get; set; }
    public Guid ExecutionId { get; set; }
    public FlowExecution Execution { get; set; } = null!;

    public Guid TaskId { get; set; }
    public TaskDefinition Task { get; set; } = null!;

    public TaskStatus Status { get; set; } = TaskStatus.NotStarted;
    public string? ResultMessage { get; set; }

    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }

    public Dictionary<string, object> InputSnapshot { get; set; } = [];
    public Dictionary<string, object> OutputSnapshot { get; set; } = [];
}
