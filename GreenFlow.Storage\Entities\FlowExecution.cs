﻿using GreenFlow.Shared;

namespace GreenFlow.Storage.Entities;

public class FlowExecution
{
    public Guid Id { get; set; }
    public Guid FlowId { get; set; }
    public FlowDefinition Flow { get; set; } = null!;
    public ExecutionStatus Status { get; set; } = ExecutionStatus.Running;
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; }
    public Dictionary<string, object> Input { get; set; } = [];
    public Dictionary<string, object> Context { get; set; } = [];
}