using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.Engine.Registry;

/// <summary>
/// Registry for task action handlers.
/// </summary>
public interface ITaskHandlerRegistry
{
    /// <summary>
    /// Registers a task handler for a specific action type.
    /// </summary>
    void RegisterHandler<TPayload>(ActionType actionType, ITaskActionHandler<TPayload> handler)
        where TPayload : ITaskPayload;

    /// <summary>
    /// Gets a task handler for the specified action type.
    /// </summary>
    ITaskActionHandler<TPayload>? GetHandler<TPayload>(ActionType actionType)
        where TPayload : ITaskPayload;

    /// <summary>
    /// Gets a task handler for the specified action type (non-generic).
    /// </summary>
    object? GetHandler(ActionType actionType);

    /// <summary>
    /// Checks if a handler is registered for the specified action type.
    /// </summary>
    bool <PERSON>(ActionType actionType);

    /// <summary>
    /// Gets all registered action types.
    /// </summary>
    IEnumerable<ActionType> GetRegisteredActionTypes();
}