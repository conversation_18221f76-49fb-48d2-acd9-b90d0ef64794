using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GreenFlow.Api.Models;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Endpoints;

public static class TenantsEndpoints
{
    public static void MapTenantsEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/tenants").WithTags("Tenants");

        group.MapGet("/", async (FlowDbContext db, IMapper mapper) =>
        {
            var tenants = await db.Tenants.ToListAsync();
            var dtos = mapper.Map<List<TenantDto>>(tenants);
            return Results.Ok(dtos);
        })
        .WithName("GetTenants");

        group.MapGet("/{id:guid}", async (Guid id, FlowDbContext db, IMapper mapper) =>
        {
            var tenant = await db.Tenants.FindAsync(id);
            return tenant is not null
                ? Results.Ok(mapper.Map<TenantDto>(tenant))
                : Results.NotFound();
        })
        .WithName("GetTenantById");

        group.MapPost("/", async (CreateTenantDto createDto, FlowDbContext db, IMapper mapper) =>
        {
            var tenant = mapper.Map<Tenant>(createDto);
            tenant.Id = Guid.NewGuid();
            tenant.LastReset = DateTime.UtcNow;
            db.Tenants.Add(tenant);
            await db.SaveChangesAsync();
            var result = mapper.Map<TenantDto>(tenant);
            return Results.Created($"/api/tenants/{tenant.Id}", result);
        })
        .WithName("CreateTenant");

        group.MapPut("/{id:guid}", async (Guid id, UpdateTenantDto updateDto, FlowDbContext db, IMapper mapper) =>
        {
            var tenant = await db.Tenants.FindAsync(id);
            if (tenant is null)
                return Results.NotFound();

            mapper.Map(updateDto, tenant);
            await db.SaveChangesAsync();
            return Results.Ok(mapper.Map<TenantDto>(tenant));
        })
        .WithName("UpdateTenant");

        group.MapDelete("/{id:guid}", async (Guid id, FlowDbContext db) =>
        {
            var tenant = await db.Tenants.FindAsync(id);
            if (tenant is null)
                return Results.NotFound();

            db.Tenants.Remove(tenant);
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("DeleteTenant");

        // Get tenant usage statistics
        group.MapGet("/{id:guid}/usage", async (Guid id, FlowDbContext db) =>
        {
            var tenant = await db.Tenants.FindAsync(id);
            if (tenant is null)
                return Results.NotFound();

            var currentMonth = DateTime.UtcNow.Month;
            var currentYear = DateTime.UtcNow.Year;

            var monthlyUsage = await db.TokenLedger
                .Where(tl => tl.TenantId == id && 
                            tl.Timestamp.Month == currentMonth && 
                            tl.Timestamp.Year == currentYear)
                .SumAsync(tl => tl.TokensUsed);

            var usage = new
            {
                TenantId = id,
                TenantName = tenant.Name,
                MonthlyBudget = tenant.MonthlyTokenBudget,
                TokensUsedThisMonth = monthlyUsage,
                RemainingTokens = tenant.MonthlyTokenBudget - monthlyUsage,
                UsagePercentage = (double)monthlyUsage / tenant.MonthlyTokenBudget * 100,
                LastReset = tenant.LastReset
            };

            return Results.Ok(usage);
        })
        .WithName("GetTenantUsage");

        // Reset tenant token usage
        group.MapPost("/{id:guid}/reset-usage", async (Guid id, FlowDbContext db) =>
        {
            var tenant = await db.Tenants.FindAsync(id);
            if (tenant is null)
                return Results.NotFound();

            tenant.TokensUsedThisMonth = 0;
            tenant.LastReset = DateTime.UtcNow;
            await db.SaveChangesAsync();
            return Results.NoContent();
        })
        .WithName("ResetTenantUsage");
    }
}
