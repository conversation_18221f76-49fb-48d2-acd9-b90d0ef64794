namespace GreenFlow.Storage.Entities;

/// <summary>
/// Enumeration of comparison operators for cross-field validation.
/// </summary>
public enum ComparisonOperator
{
    Equal,
    NotEqual,
    GreaterThan,
    LessThan,
    GreaterOrEqual,
    LessOrEqual
}

/// <summary>
/// Rule that enforces a relationship between two fields (e.g., A &gt;= B).
/// </summary>
public class CrossFieldRule
{
    public Guid Id { get; set; }
    /// <summary>
    /// Foreign key to the owning TaskDefinition.
    /// </summary>
    public Guid TaskId { get; set; }
    /// <summary>
    /// Navigation back to the parent task.
    /// </summary>
    public TaskDefinition Task { get; set; } = null!;
    /// <summary>
    /// First field to compare.
    /// </summary>
    public string FieldNameA { get; set; } = null!;
    /// <summary>
    /// Second field to compare.
    /// </summary>
    public string FieldNameB { get; set; } = null!;
    /// <summary>
    /// Operator used to compare the two field values.
    /// </summary>
    public ComparisonOperator Comparison { get; set; }
    /// <summary>
    /// Custom error message if the rule fails.
    /// </summary>
    public string? ErrorMessage { get; set; }
}