﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="10.0.0-preview.3.25172.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GreenFlow.Storage\GreenFlow.Storage.csproj" />
    <ProjectReference Include="..\GreenFlow.Shared\GreenFlow.Shared.csproj" />
  </ItemGroup>

</Project>
