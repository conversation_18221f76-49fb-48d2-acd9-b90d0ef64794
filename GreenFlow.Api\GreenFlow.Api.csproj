﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GreenFlow.Storage\GreenFlow.Storage.csproj" />
    <ProjectReference Include="..\GreenFlow.Shared\GreenFlow.Shared.csproj" />
    <ProjectReference Include="..\GreenFlow.PluginSystem\GreenFlow.PluginSystem.csproj" />
  </ItemGroup>

</Project>
