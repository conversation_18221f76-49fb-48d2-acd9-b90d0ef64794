using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Storage.Entities;

namespace GreenFlow.PluginSystem.Payloads
{
    public class ChatGptPayload : ITaskPayload
    {
        public string PromptTemplate { get; set; } = null!;
        public double Temperature { get; set; } = 0.7;
        public int MaxTokens { get; set; } = 256;
        public ActionType ActionType => ActionType.ChatGPT;
    }
}