using GreenFlow.Shared;

namespace GreenFlow.Api.Models;

public class TaskExecutionDto
{
    public Guid Id { get; set; }
    public Guid ExecutionId { get; set; }
    public Guid TaskId { get; set; }
    public string TaskName { get; set; } = string.Empty;
    public TaskStatus Status { get; set; }
    public string? ResultMessage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Dictionary<string, object> InputSnapshot { get; set; } = new();
    public Dictionary<string, object> OutputSnapshot { get; set; } = new();
    public TimeSpan? Duration => CompletedAt.HasValue ? CompletedAt.Value - StartedAt : null;
}
