namespace GreenFlow.Shared.Messages;

/// <summary>
/// Message sent to workers to execute a specific task.
/// </summary>
public class TaskExecutionMessage
{
    public Guid ExecutionId { get; set; }
    public Guid TaskId { get; set; }
    public string TaskName { get; set; } = string.Empty;
    public ActionType ActionType { get; set; }
    public string? ActionPayload { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public UserIdentity? UserIdentity { get; set; }
    public DateTime ScheduledAt { get; set; } = DateTime.UtcNow;
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
}