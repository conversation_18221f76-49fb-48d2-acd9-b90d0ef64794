using AutoMapper;
using GreenFlow.Storage.Entities;
using GreenFlow.Api.Models;

namespace GreenFlow.Api.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Domain to DTO mappings
            CreateMap<FlowDefinition, FlowDto>();
            CreateMap<TaskDefinition, TaskDto>()
                .ForMember(dest => dest.FlowName, opt => opt.MapFrom(src => src.Flow.Name));
            CreateMap<TaskEdge, TaskEdgeDto>();

            // Create DTO to Domain mappings
            CreateMap<CreateFlowDto, FlowDefinition>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Tasks, opt => opt.Ignore())
                .ForMember(dest => dest.Edges, opt => opt.Ignore());
            CreateMap<CreateTaskDto, TaskDefinition>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.SubTasks, opt => opt.Ignore())
                .ForMember(dest => dest.CoordinationRule, opt => opt.Ignore());
        }
    }
}