using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to invoke ChatGPT based on ChatGptPayload.
    /// </summary>
    public class ChatGptPlugin : ITaskActionHandler<ChatGptPayload>
    {
        public Task HandleAsync(ChatGptPayload payload, IDictionary<string, object> context)
        {
            // TODO: Integrate with ChatGPT API
            context["ChatGptResult"] = string.Empty;
            return Task.CompletedTask;
        }
    }
}