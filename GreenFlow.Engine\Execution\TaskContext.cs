using GreenFlow.Shared;

namespace GreenFlow.Engine.Execution;

/// <summary>
/// Execution context for a task, containing all necessary information for task execution.
/// </summary>
public class TaskContext
{
    public Guid ExecutionId { get; set; }
    public Guid TaskId { get; set; }
    public string TaskName { get; set; } = string.Empty;
    public ActionType ActionType { get; set; }
    public string? ActionPayload { get; set; }

    /// <summary>
    /// The execution context shared across all tasks in the flow.
    /// </summary>
    public Dictionary<string, object> FlowContext { get; set; } = new();

    /// <summary>
    /// Task-specific input data.
    /// </summary>
    public Dictionary<string, object> TaskInput { get; set; } = new();

    /// <summary>
    /// Task-specific output data.
    /// </summary>
    public Dictionary<string, object> TaskOutput { get; set; } = new();

    /// <summary>
    /// User identity information for the current execution.
    /// </summary>
    public UserIdentity? UserIdentity { get; set; }

    /// <summary>
    /// Current task status.
    /// </summary>
    public GreenFlow.Shared.TaskStatus Status { get; set; } = GreenFlow.Shared.TaskStatus.NotStarted;

    /// <summary>
    /// Task execution start time.
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// Task execution completion time.
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Error message if task failed.
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Number of retry attempts for this task.
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Maximum number of retries allowed.
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Cost incurred by this task execution.
    /// </summary>
    public double? CostIncurred { get; set; }
}