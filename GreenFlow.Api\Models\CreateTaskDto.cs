using System;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Models
{
    // DTO for creating a new task (associates with existing flow via FlowId)
    public class CreateTaskDto
    {
        public Guid FlowId { get; set; }
        public string Name { get; set; } = string.Empty;
        public ActionType ActionType { get; set; }
        public string? ActionPayload { get; set; }
        public Guid? ParentTaskId { get; set; }
        public double? RuntimeCostMultiplier { get; set; }
        public int PositionX { get; set; }
        public int PositionY { get; set; }
    }
}