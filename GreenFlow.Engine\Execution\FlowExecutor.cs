using GreenFlow.Engine.Routing;
using GreenFlow.Shared;
using GreenFlow.Shared.Messages;
using GreenFlow.Storage.Context;
using GreenFlow.Storage.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GreenFlow.Engine.Execution;

/// <summary>
/// Core flow execution engine that orchestrates task execution.
/// </summary>
public class FlowExecutor
{
    private readonly FlowDbContext _dbContext;
    private readonly ITaskRouter _taskRouter;
    private readonly ILogger<FlowExecutor> _logger;

    public FlowExecutor(
        FlowDbContext dbContext,
        ITaskRouter taskRouter,
        ILogger<FlowExecutor> logger)
    {
        _dbContext = dbContext;
        _taskRouter = taskRouter;
        _logger = logger;
    }

    /// <summary>
    /// Starts execution of a flow.
    /// </summary>
    public async Task<bool> StartFlowAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        var execution = await _dbContext.Executions
            .Include(e => e.Flow)
            .ThenInclude(f => f.Tasks)
            .Include(e => e.Flow)
            .ThenInclude(f => f.Edges)
            .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

        if (execution == null)
        {
            _logger.LogError("Flow execution {ExecutionId} not found", executionId);
            return false;
        }

        if (execution.Status != ExecutionStatus.Running)
        {
            _logger.LogWarning("Flow execution {ExecutionId} is not in Running status: {Status}",
                executionId, execution.Status);
            return false;
        }

        _logger.LogInformation("Starting flow execution {ExecutionId} for flow {FlowName}",
            executionId, execution.Flow.Name);

        // Find the start task
        var startTask = execution.Flow.Tasks.FirstOrDefault(t => t.Id == execution.Flow.StartTaskId);
        if (startTask == null)
        {
            _logger.LogError("No start task found for flow {FlowId}", execution.FlowId);
            await MarkExecutionFailedAsync(execution, "No start task defined");
            return false;
        }

        // Execute the start task
        await ExecuteTaskAsync(execution, startTask, cancellationToken);
        return true;
    }

    /// <summary>
    /// Executes a specific task within a flow.
    /// </summary>
    private async Task ExecuteTaskAsync(FlowExecution execution, TaskDefinition task, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing task {TaskName} ({TaskId}) in execution {ExecutionId}",
            task.Name, task.Id, execution.Id);

        // Create or update task execution record
        var taskExecution = await GetOrCreateTaskExecutionAsync(execution.Id, task.Id);
        taskExecution.Status = GreenFlow.Shared.TaskStatus.Running;
        taskExecution.StartedAt = DateTime.UtcNow;
        await _dbContext.SaveChangesAsync(cancellationToken);

        try
        {
            // Create task execution message
            var message = new TaskExecutionMessage
            {
                ExecutionId = execution.Id,
                TaskId = task.Id,
                TaskName = task.Name,
                ActionType = task.ActionType,
                ActionPayload = task.ActionPayload,
                Context = execution.Context,
                ScheduledAt = DateTime.UtcNow
            };

            // Route task to appropriate worker
            await _taskRouter.RouteAsync(message, GetRoutingKey(task.ActionType));

            _logger.LogInformation("Task {TaskId} routed for execution", task.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to route task {TaskId} for execution", task.Id);
            await MarkTaskFailedAsync(taskExecution, ex.Message);
        }
    }

    /// <summary>
    /// Handles task completion and determines next tasks to execute.
    /// </summary>
    public async Task HandleTaskCompletionAsync(TaskCompletedMessage message, CancellationToken cancellationToken = default)
    {
        var taskExecution = await _dbContext.TaskExecutions
            .Include(te => te.Execution)
            .ThenInclude(e => e.Flow)
            .ThenInclude(f => f.Tasks)
            .Include(te => te.Execution)
            .ThenInclude(e => e.Flow)
            .ThenInclude(f => f.Edges)
            .Include(te => te.Task)
            .FirstOrDefaultAsync(te => te.ExecutionId == message.ExecutionId && te.TaskId == message.TaskId, cancellationToken);

        if (taskExecution == null)
        {
            _logger.LogError("Task execution not found: ExecutionId={ExecutionId}, TaskId={TaskId}",
                message.ExecutionId, message.TaskId);
            return;
        }

        // Update task execution
        taskExecution.Status = message.Status;
        taskExecution.CompletedAt = message.CompletedAt;
        taskExecution.ResultMessage = message.ResultMessage;
        taskExecution.OutputSnapshot = message.OutputData;

        // Update flow context with task output
        foreach (var kvp in message.OutputData)
        {
            taskExecution.Execution.Context[kvp.Key] = kvp.Value;
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Task {TaskId} completed with status {Status}", message.TaskId, message.Status);

        // Determine next tasks based on task completion status
        await ProcessTaskEdgesAsync(taskExecution, cancellationToken);
    }

    /// <summary>
    /// Processes outgoing edges from a completed task to determine next tasks.
    /// </summary>
    private async Task ProcessTaskEdgesAsync(TaskExecution taskExecution, CancellationToken cancellationToken)
    {
        var edges = taskExecution.Execution.Flow.Edges
            .Where(e => e.FromTaskId == taskExecution.TaskId)
            .ToList();

        var edgesToFollow = new List<TaskEdge>();

        foreach (var edge in edges)
        {
            bool shouldFollow = edge.Type switch
            {
                EdgeType.OnSuccess => taskExecution.Status == GreenFlow.Shared.TaskStatus.Success,
                EdgeType.OnFailure => taskExecution.Status == GreenFlow.Shared.TaskStatus.Failed,
                EdgeType.OnFailureCount => await ShouldFollowFailureCountEdgeAsync(edge, taskExecution, cancellationToken),
                _ => false
            };

            if (shouldFollow)
            {
                edgesToFollow.Add(edge);
            }
        }

        // Execute next tasks
        foreach (var edge in edgesToFollow)
        {
            var nextTask = taskExecution.Execution.Flow.Tasks.FirstOrDefault(t => t.Id == edge.ToTaskId);
            if (nextTask != null)
            {
                await ExecuteTaskAsync(taskExecution.Execution, nextTask, cancellationToken);
            }
        }

        // Check if flow is complete
        await CheckFlowCompletionAsync(taskExecution.Execution, cancellationToken);
    }

    private async Task<bool> ShouldFollowFailureCountEdgeAsync(TaskEdge edge, TaskExecution taskExecution, CancellationToken cancellationToken)
    {
        if (edge.FailureThreshold == null || taskExecution.Status != GreenFlow.Shared.TaskStatus.Failed)
            return false;

        var failureCount = await _dbContext.TaskExecutions
            .Where(te => te.ExecutionId == taskExecution.ExecutionId &&
                        te.TaskId == taskExecution.TaskId &&
                        te.Status == GreenFlow.Shared.TaskStatus.Failed)
            .CountAsync(cancellationToken);

        return failureCount >= edge.FailureThreshold;
    }

    private async Task CheckFlowCompletionAsync(FlowExecution execution, CancellationToken cancellationToken)
    {
        var allTaskExecutions = await _dbContext.TaskExecutions
            .Where(te => te.ExecutionId == execution.Id)
            .ToListAsync(cancellationToken);

        var pendingTasks = allTaskExecutions.Where(te => te.Status == GreenFlow.Shared.TaskStatus.Running || te.Status == GreenFlow.Shared.TaskStatus.NotStarted).ToList();

        if (!pendingTasks.Any())
        {
            // All tasks completed, determine final status
            var hasFailures = allTaskExecutions.Any(te => te.Status == GreenFlow.Shared.TaskStatus.Failed);
            execution.Status = hasFailures ? ExecutionStatus.Failed : ExecutionStatus.Completed;
            execution.CompletedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Flow execution {ExecutionId} completed with status {Status}",
                execution.Id, execution.Status);
        }
    }

    private async Task<TaskExecution> GetOrCreateTaskExecutionAsync(Guid executionId, Guid taskId)
    {
        var taskExecution = await _dbContext.TaskExecutions
            .FirstOrDefaultAsync(te => te.ExecutionId == executionId && te.TaskId == taskId);

        if (taskExecution == null)
        {
            taskExecution = new TaskExecution
            {
                Id = Guid.NewGuid(),
                ExecutionId = executionId,
                TaskId = taskId,
                Status = GreenFlow.Shared.TaskStatus.NotStarted,
                StartedAt = DateTime.UtcNow
            };
            _dbContext.TaskExecutions.Add(taskExecution);
        }

        return taskExecution;
    }

    private async Task MarkExecutionFailedAsync(FlowExecution execution, string errorMessage)
    {
        execution.Status = ExecutionStatus.Failed;
        execution.CompletedAt = DateTime.UtcNow;
        await _dbContext.SaveChangesAsync();

        _logger.LogError("Flow execution {ExecutionId} failed: {ErrorMessage}", execution.Id, errorMessage);
    }

    private async Task MarkTaskFailedAsync(TaskExecution taskExecution, string errorMessage)
    {
        taskExecution.Status = GreenFlow.Shared.TaskStatus.Failed;
        taskExecution.CompletedAt = DateTime.UtcNow;
        taskExecution.ResultMessage = errorMessage;
        await _dbContext.SaveChangesAsync();
    }

    private static string GetRoutingKey(ActionType actionType)
    {
        return $"task.{actionType.ToString().ToLowerInvariant()}";
    }
}