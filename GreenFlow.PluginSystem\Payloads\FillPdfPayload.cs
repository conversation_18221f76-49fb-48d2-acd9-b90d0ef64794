using System.Collections.Generic;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for FillPdf tasks.
    /// </summary>
    public class FillPdfPayload : ITaskPayload
    {
        /// <summary>
        /// The PDF template identifier or path.
        /// </summary>
        public string PdfTemplate { get; set; } = null!;

        /// <summary>
        /// Field values to populate in the PDF.
        /// </summary>
        public Dictionary<string, object> Fields { get; set; } = new();

        /// <summary>
        /// Optional output filename or path.
        /// </summary>
        public string? OutputPath { get; set; }
        /// <inheritdoc />
        public ActionType ActionType => ActionType.FillPdf;
    }
}