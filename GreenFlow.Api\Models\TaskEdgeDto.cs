using System;
using GreenFlow.Storage.Entities;

namespace GreenFlow.Api.Models
{
    public class TaskEdgeDto
    {
        public Guid Id { get; set; }
        public Guid FromTaskId { get; set; }
        public Guid ToTaskId { get; set; }
        /// <summary>
        /// When to follow this edge: OnSuccess, OnFailure, or OnFailureCount.
        /// </summary>
        public EdgeType Type { get; set; }
        /// <summary>
        /// For OnFailureCount edges, number of failed executions required before this edge is taken.
        /// </summary>
        public int? FailureThreshold { get; set; }
        /// <summary>
        /// A logical condition expression evaluated at runtime (if Type == Conditional).
        /// </summary>
        public string? Condition { get; set; }
        /// <summary>
        /// Label for Labeled edges (if used).
        /// </summary>
        public string? Label { get; set; }
    }
}