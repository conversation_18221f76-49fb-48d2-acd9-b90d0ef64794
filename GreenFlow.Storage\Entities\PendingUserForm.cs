﻿namespace GreenFlow.Storage.Entities;

public class PendingUserForm
{
    public Guid Id { get; set; }
    public Guid ExecutionId { get; set; }
    public Guid TaskId { get; set; }

    public string Assignee { get; set; } = null!;
    public bool Completed { get; set; }

    public Dictionary<string, object> Input { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
