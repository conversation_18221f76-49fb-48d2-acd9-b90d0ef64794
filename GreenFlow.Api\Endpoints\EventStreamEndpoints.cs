using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace GreenFlow.Api.Endpoints;

public static class EventStreamEndpoints
{
    public static void MapEventStreamEndpoints(this WebApplication app)
    {
        var group = app.MapGroup("/api/events").WithTags("Events");

        group.MapGet("/", async (HttpContext http) =>
        {
            http.Response.ContentType = "text/event-stream";
            await http.Response.WriteAsync("event: open\n");
            await http.Response.WriteAsync($"data: Connected at {DateTime.UtcNow}\n\n");
            await http.Response.Body.FlushAsync();
        })
        .WithName("GetEventStream");
    }
}
