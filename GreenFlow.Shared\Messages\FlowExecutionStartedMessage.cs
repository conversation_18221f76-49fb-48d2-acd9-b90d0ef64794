namespace GreenFlow.Shared.Messages;

/// <summary>
/// Message sent when a flow execution starts.
/// </summary>
public class FlowExecutionStartedMessage
{
    public Guid ExecutionId { get; set; }
    public Guid FlowId { get; set; }
    public string FlowName { get; set; } = string.Empty;
    public Dictionary<string, object> Input { get; set; } = new();
    public UserIdentity? UserIdentity { get; set; }
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
}
