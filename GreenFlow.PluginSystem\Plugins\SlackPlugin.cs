using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to send Slack notifications.
    /// </summary>
    public class SlackPlugin : ITaskActionHandler<SlackPayload>
    {
        public Task HandleAsync(SlackPayload payload, IDictionary<string, object> context)
        {
            // TODO: Integrate with Slack API
            // For now, just store the message details in context
            context["SlackMessage"] = new
            {
                payload.Channel,
                payload.Message,
                payload.Username,
                payload.IconEmoji,
                payload.Attachments,
                SentAt = DateTime.UtcNow
            };

            return Task.CompletedTask;
        }
    }
}
