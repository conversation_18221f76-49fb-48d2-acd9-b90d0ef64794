using GreenFlow.Shared;

namespace GreenFlow.Engine.Execution;

/// <summary>
/// Information about why a flow execution was paused.
/// </summary>
public class PauseInfo
{
    public PauseReason Reason { get; set; }
    public string? Message { get; set; }
    public DateTime PausedAt { get; set; } = DateTime.UtcNow;
    public Guid? TaskId { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}