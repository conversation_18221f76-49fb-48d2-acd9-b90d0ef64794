FROM mcr.microsoft.com/dotnet/aspnet:10.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:10.0 AS build
WORKDIR /src
COPY ["GreenFlow.Storage/GreenFlow.Storage.csproj", "GreenFlow.Storage/"]
COPY ["GreenFlow.Api/GreenFlow.Api.csproj", "GreenFlow.Api/"]
RUN dotnet restore "GreenFlow.Api/GreenFlow.Api.csproj"
COPY . .
WORKDIR "/src/GreenFlow.Api"
RUN dotnet build "GreenFlow.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "GreenFlow.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "GreenFlow.Api.dll"]