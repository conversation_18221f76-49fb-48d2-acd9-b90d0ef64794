﻿namespace GreenFlow.Storage.Entities;
public class TaskEdge
{
    /// <summary>
    /// Primary key for this edge.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Type of edge: OnSuccess, OnFailure, or OnFailureCount.
    /// </summary>
    public EdgeType Type { get; set; } = EdgeType.OnSuccess;

    /// <summary>
    /// For OnFailureCount edges, the number of failed executions required before this edge is taken.
    /// </summary>
    public int? FailureThreshold { get; set; }
    public Guid FromTaskId { get; set; }
    public TaskDefinition FromTask { get; set; } = null!;

    public Guid ToTaskId { get; set; }
    public TaskDefinition ToTask { get; set; } = null!;

    public string? Condition { get; set; } // For conditional branching
    public string? Label { get; set; }     // e.g., "onSuccess"
}
