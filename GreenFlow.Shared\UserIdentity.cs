namespace GreenFlow.Shared;

/// <summary>
/// Represents user identity information for task execution context.
/// </summary>
public class UserIdentity
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public Dictionary<string, object> Claims { get; set; } = new();
}
