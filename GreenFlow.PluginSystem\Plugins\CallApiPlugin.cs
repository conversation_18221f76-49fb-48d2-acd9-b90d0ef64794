using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to call external APIs based on CallApiPayload.
    /// </summary>
    public class CallApiPlugin : ITaskActionHandler<CallApiPayload>
    {
        public Task HandleAsync(CallApiPayload payload, IDictionary<string, object> context)
        {
            // TODO: Perform HTTP call
            context["ApiResponse"] = null;
            return Task.CompletedTask;
        }
    }
}