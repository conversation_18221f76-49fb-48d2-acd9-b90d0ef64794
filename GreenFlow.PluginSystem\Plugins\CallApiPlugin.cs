using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to call external APIs based on CallApiPayload.
    /// </summary>
    public class CallApiPlugin : ITaskActionHandler<CallApiPayload>
    {
        private readonly HttpClient _httpClient;

        public CallApiPlugin(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task HandleAsync(CallApiPayload payload, IDictionary<string, object> context)
        {
            try
            {
                var request = new HttpRequestMessage(new HttpMethod(payload.Method), payload.Url);

                // Add headers
                if (payload.Headers != null)
                {
                    foreach (var header in payload.Headers)
                    {
                        request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                // Add body for POST/PUT requests
                if (payload.Body != null && (payload.Method.Equals("POST", StringComparison.OrdinalIgnoreCase) ||
                                           payload.Method.Equals("PUT", StringComparison.OrdinalIgnoreCase)))
                {
                    var jsonContent = JsonSerializer.Serialize(payload.Body);
                    request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                context["ApiResponse"] = new
                {
                    StatusCode = (int)response.StatusCode,
                    IsSuccess = response.IsSuccessStatusCode,
                    Content = responseContent,
                    Headers = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value)),
                    RequestUrl = payload.Url,
                    RequestMethod = payload.Method
                };
            }
            catch (Exception ex)
            {
                context["ApiResponse"] = new
                {
                    StatusCode = 0,
                    IsSuccess = false,
                    Error = ex.Message,
                    RequestUrl = payload.Url,
                    RequestMethod = payload.Method
                };
            }
        }
    }
}