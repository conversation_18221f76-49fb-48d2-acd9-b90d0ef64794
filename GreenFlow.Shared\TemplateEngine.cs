using System.Text.RegularExpressions;

namespace GreenFlow.Shared;

/// <summary>
/// Simple template engine for variable substitution in strings.
/// </summary>
public static class TemplateEngine
{
    private static readonly Regex VariablePattern = new(@"\{\{(\w+)\}\}", RegexOptions.Compiled);

    /// <summary>
    /// Replaces {{variableName}} placeholders with values from the context.
    /// </summary>
    /// <param name="template">The template string with {{variable}} placeholders</param>
    /// <param name="context">Dictionary containing variable values</param>
    /// <returns>The template with variables replaced</returns>
    public static string Render(string template, IDictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(template))
            return template;

        return VariablePattern.Replace(template, match =>
        {
            var variableName = match.Groups[1].Value;
            if (context.TryGetValue(variableName, out var value))
            {
                return value?.ToString() ?? string.Empty;
            }
            return match.Value; // Keep original if variable not found
        });
    }

    /// <summary>
    /// Extracts all variable names from a template string.
    /// </summary>
    /// <param name="template">The template string</param>
    /// <returns>List of variable names found in the template</returns>
    public static IEnumerable<string> ExtractVariables(string template)
    {
        if (string.IsNullOrEmpty(template))
            return Enumerable.Empty<string>();

        return VariablePattern.Matches(template)
            .Cast<Match>()
            .Select(m => m.Groups[1].Value)
            .Distinct();
    }
}
