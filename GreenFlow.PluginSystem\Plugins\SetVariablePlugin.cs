using System.Collections.Generic;
using System.Threading.Tasks;
using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.PluginSystem.Payloads;

namespace GreenFlow.PluginSystem.Plugins
{
    /// <summary>
    /// Plugin to set variables in the execution context.
    /// </summary>
    public class SetVariablePlugin : ITaskActionHandler<SetVariablePayload>
    {
        public Task HandleAsync(SetVariablePayload payload, IDictionary<string, object> context)
        {
            // Set all variables from the payload into the context
            foreach (var variable in payload.Variables)
            {
                context[variable.Key] = variable.Value;
            }

            return Task.CompletedTask;
        }
    }
}
