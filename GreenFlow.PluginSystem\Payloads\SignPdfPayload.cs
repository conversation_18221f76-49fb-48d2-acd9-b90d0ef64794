using GreenFlow.PluginSystem.Interfaces;
using GreenFlow.Shared;

namespace GreenFlow.PluginSystem.Payloads
{
    /// <summary>
    /// Payload for PDF signing tasks.
    /// </summary>
    public class SignPdfPayload : ITaskPayload
    {
        /// <summary>
        /// The path or identifier of the PDF to sign.
        /// </summary>
        public string PdfPath { get; set; } = null!;

        /// <summary>
        /// The certificate to use for signing.
        /// </summary>
        public string CertificatePath { get; set; } = null!;

        /// <summary>
        /// Password for the certificate.
        /// </summary>
        public string? CertificatePassword { get; set; }

        /// <summary>
        /// Reason for signing.
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Location where the signing takes place.
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// Contact information of the signer.
        /// </summary>
        public string? ContactInfo { get; set; }

        /// <summary>
        /// Output path for the signed PDF.
        /// </summary>
        public string? OutputPath { get; set; }

        /// <summary>
        /// Position of the signature on the page.
        /// </summary>
        public SignaturePosition? Position { get; set; }

        /// <inheritdoc />
        public ActionType ActionType => ActionType.SignPdf;
    }

    /// <summary>
    /// Represents the position of a signature on a PDF page.
    /// </summary>
    public class SignaturePosition
    {
        public int Page { get; set; } = 1;
        public float X { get; set; }
        public float Y { get; set; }
        public float Width { get; set; } = 100;
        public float Height { get; set; } = 50;
    }
}
